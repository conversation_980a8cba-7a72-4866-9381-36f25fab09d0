"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Locale } from "@/i18n-config";
import { useRouter } from "next/navigation";
import {
  Activity,
  ArrowLeft,
  Database,
  CheckCircle,
  Clock,
  AlertTriangle,
  FileText,
  Users,
  Save,
  X,
  Calendar,
  Mail,
  CheckCircle2,
  XCircle,
  Circle,
  Loader,
  Download,
  Eye,
  Unlink,
  ChevronDown,
  ChevronRight,
  BarChart3,
  TrendingUp,
  Link,
  CalendarDays,
  PieChart,
  Target,
  Plus,
  User,
  Trash2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { SystemsService, System, SystemTaskStatus, ApprovalDelayEntry } from "@/Firebase/firestore/SystemsService";
import { MeetingsService, Meeting } from "@/Firebase/firestore/services/MeetingsService";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Timestamp } from "firebase/firestore";
import { SystemTasksJourney, PhaseStatus } from "@/components/SystemTasks/SystemTasksJourney";
import ExcelJS from 'exceljs';
import {
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  BarChart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';


// Type definitions for the new task structure
type TaskTypeKey = 'classificationDataStatus' | 'initialClassificationStatus' | 'furtherClassificationStatus' | 'classificationReviewStatus' | 'finalApprovalStatus';

interface TaskType {
  key: TaskTypeKey;
  label: {
    en: string;
    ar: string;
  };
  statuses: {
    value: SystemTaskStatus;
    label: {
      en: string;
      ar: string;
    };
  }[];
  hasLink?: boolean;
}

interface SystemsPageProps {
  params: Promise<{ lang: Locale }>;
}

interface NotesModalData {
  systemId: string;
  taskType: TaskTypeKey;
  currentNote: string;
}

interface MeetingModalData {
  systemId: string;
  currentMeetingId?: string;
}

interface DateEditModalData {
  systemId: string;
  taskType: TaskTypeKey;
  currentStartDate?: Timestamp;
  currentEndDate?: Timestamp;
}

interface ApprovalDelayModalData {
  systemId: string;
  currentReason: string;
}

interface DelayHistoryModalData {
  systemId: string;
  systemName: string;
  delayHistory: ApprovalDelayEntry[];
}

interface BulkUpdateModalData {
  groupNumber: string;
  taskType: TaskTypeKey;
  groupSystems: System[];
}



export default function SystemsPage({ params }: SystemsPageProps) {
  const [lang, setLang] = useState<string>('');
  const [systems, setSystems] = useState<System[]>([]);
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [notesModal, setNotesModal] = useState<NotesModalData | null>(null);
  const [meetingModal, setMeetingModal] = useState<MeetingModalData | null>(null);
  const [meetingDetailsModal, setMeetingDetailsModal] = useState<{meeting: Meeting, systemId: string} | null>(null);
  const [linkModal, setLinkModal] = useState<{systemId: string, taskType: string, currentLink?: string} | null>(null);
  const [dateEditModal, setDateEditModal] = useState<DateEditModalData | null>(null);
  const [approvalDelayModal, setApprovalDelayModal] = useState<ApprovalDelayModalData | null>(null);
  const [delayHistoryModal, setDelayHistoryModal] = useState<DelayHistoryModalData | null>(null);
  const [editingDelayEntry, setEditingDelayEntry] = useState<{entryId: string, reason: string} | null>(null);
  const [bulkUpdateModal, setBulkUpdateModal] = useState<BulkUpdateModalData | null>(null);
  const [tempNote, setTempNote] = useState('');
  const [tempLink, setTempLink] = useState('');
  const [tempStartDate, setTempStartDate] = useState('');
  const [tempEndDate, setTempEndDate] = useState('');
  const [tempDelayReason, setTempDelayReason] = useState('');
  const [bulkStatus, setBulkStatus] = useState<SystemTaskStatus>(SystemTaskStatus.NOT_STARTED);
  const [bulkNote, setBulkNote] = useState('');
  const [bulkLink, setBulkLink] = useState('');
  const [bulkStartDate, setBulkStartDate] = useState('');
  const [bulkEndDate, setBulkEndDate] = useState('');
  const [isBulkUpdating, setIsBulkUpdating] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [minimizedGroups, setMinimizedGroups] = useState<Set<string>>(new Set());
  const [progressBreakdownModal, setProgressBreakdownModal] = useState(false);
  const [groupDetailModal, setGroupDetailModal] = useState<{
    groupId: string;
    groupName: string;
    systems: System[];
    progress: number;
  } | null>(null);
  const [activeTab, setActiveTab] = useState<string>("dashboard");

  const router = useRouter();
  const { toast } = useToast();

  // Task types for the new structure
  const taskTypes: TaskType[] = [
    {
      key: 'classificationDataStatus',
      label: { en: 'Classification Data Supply From Nouf', ar: 'توريد بيانات التصنيف من نوف' },
      statuses: [
        { value: SystemTaskStatus.NOT_STARTED, label: { en: 'Not Started', ar: 'لم يبدأ' } },
        { value: SystemTaskStatus.DATA_REQUESTED, label: { en: 'Data Requested', ar: 'تم طلب البيانات' } },
        { value: SystemTaskStatus.DATA_RECEIVED, label: { en: 'Data Received', ar: 'تم استلام البيانات' } }
      ]
    },
    {
      key: 'initialClassificationStatus',
      label: { en: 'Initial Classification', ar: 'التصنيف الأولي' },
      statuses: [
        { value: SystemTaskStatus.NOT_STARTED, label: { en: 'Not Started', ar: 'لم يبدأ' } },
        { value: SystemTaskStatus.IN_PROGRESS, label: { en: 'In Progress', ar: 'قيد التنفيذ' } },
        { value: SystemTaskStatus.DONE, label: { en: 'Done', ar: 'مكتمل' } }
      ]
    },
    {
      key: 'furtherClassificationStatus',
      label: { en: 'Further Personal Data Classification', ar: 'تصنيف البيانات الشخصية الإضافي' },
      statuses: [
        { value: SystemTaskStatus.NOT_STARTED, label: { en: 'Not Started', ar: 'لم يبدأ' } },
        { value: SystemTaskStatus.IN_PROGRESS, label: { en: 'In Progress', ar: 'قيد التنفيذ' } },
        { value: SystemTaskStatus.DONE, label: { en: 'Done', ar: 'مكتمل' } }
      ],
      hasLink: true
    },
    {
      key: 'classificationReviewStatus',
      label: { en: 'Classification Data Sent To Business Owners', ar: 'إرسال بيانات التصنيف لأصحاب الأعمال' },
      statuses: [
        { value: SystemTaskStatus.NOT_STARTED, label: { en: 'Not Started', ar: 'لم يبدأ' } },
        { value: SystemTaskStatus.DATA_SENT_AWAITING_REPLY, label: { en: 'Data Sent Awaiting Reply', ar: 'تم إرسال البيانات في انتظار الرد' } },
        { value: SystemTaskStatus.MEETING_SCHEDULED, label: { en: 'Meeting Scheduled', ar: 'تم جدولة الاجتماع' } },
        { value: SystemTaskStatus.MEETING_DONE, label: { en: 'Meeting Done', ar: 'تم الاجتماع' } },
        { value: SystemTaskStatus.NO_REPLY_RECEIVED, label: { en: 'No Reply Received', ar: 'لم يتم استلام رد' } },
        { value: SystemTaskStatus.DONE, label: { en: 'Done', ar: 'مكتمل' } }
      ],
      hasLink: true
    },
    {
      key: 'finalApprovalStatus',
      label: { en: 'Final Approval Received?', ar: 'تم استلام الموافقة النهائية؟' },
      statuses: [
        { value: SystemTaskStatus.NOT_STARTED, label: { en: 'Not Started', ar: 'لم يبدأ' } },
        { value: SystemTaskStatus.IN_PROGRESS, label: { en: 'Approval Expected Within 2 Days', ar: 'الموافقة متوقعة خلال يومين' } },
        { value: SystemTaskStatus.APPROVAL_DELAYED, label: { en: 'Approval Delayed', ar: 'تأخر الموافقة' } },
        { value: SystemTaskStatus.DONE, label: { en: 'Received Approval', ar: 'تم استلام الموافقة' } }
      ],
      hasLink: true
    }
  ];

  // Initialize params
  useEffect(() => {
    params.then(({ lang }) => {
      setLang(lang);
    });
  }, [params]);

  // Load systems and meetings data
  const loadData = useCallback(async () => {
    try {
      setIsLoading(true);
      const [allSystems, allMeetings] = await Promise.all([
        SystemsService.getSystems(),
        MeetingsService.getMeetings()
      ]);

      // Check for overdue replies before setting systems
      await SystemsService.checkAndUpdateOverdueReplies();

      setSystems(allSystems);
      setMeetings(allMeetings);
    } catch (error) {
      console.error('Error loading data:', error);
      if (lang) {
        const isRTL = lang === "ar";
        toast({
          title: isRTL ? "خطأ في تحميل البيانات" : "Error loading data",
          description: isRTL ? "فشل في تحميل بيانات الأنظمة والاجتماعات" : "Failed to load systems and meetings data",
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
    }
  }, [lang, toast]);

  // Load data when component mounts or page changes
  useEffect(() => {
    if (lang) {
      loadData();
    }
  }, [lang, loadData]);

  // Don't render until params are loaded
  if (!lang) {
    return <div>Loading...</div>;
  }

  const isRTL = lang === "ar";

  // Calculate statistics
  const calculateStats = () => {
    const stats = {
      totalSystems: systems.length,
      completedSystems: 0,
      pendingTasks: 0,
      notStartedTasks: 0,
      totalTasks: 0,
      completedTasks: 0,
      overallCompletionPercentage: 0,
      taskBreakdown: {} as Record<string, { completed: number; total: number; percentage: number }>
    };

    // Initialize task breakdown
    taskTypes.forEach(taskType => {
      stats.taskBreakdown[taskType.key] = {
        completed: 0,
        total: systems.length,
        percentage: 0
      };
    });

    systems.forEach(system => {
      if (SystemsService.isSystemCompleted(system)) {
        stats.completedSystems++;
      }

      taskTypes.forEach(taskType => {
        const status = system[taskType.key as keyof System] as SystemTaskStatus;
        stats.totalTasks++;

        // Count completed tasks (DONE and DATA_RECEIVED are considered completed)
        if (status === SystemTaskStatus.DONE || status === SystemTaskStatus.DATA_RECEIVED) {
          stats.completedTasks++;
          stats.taskBreakdown[taskType.key].completed++;
        }

        switch (status) {
          case SystemTaskStatus.DATA_REQUESTED:
          case SystemTaskStatus.DATA_RECEIVED:
          case SystemTaskStatus.IN_PROGRESS:
          case SystemTaskStatus.DATA_SENT_AWAITING_REPLY:
          case SystemTaskStatus.MEETING_SCHEDULED:
          case SystemTaskStatus.MEETING_DONE:
          case SystemTaskStatus.NO_REPLY_RECEIVED:
          case SystemTaskStatus.APPROVAL_DELAYED:
            stats.pendingTasks++;
            break;
          case SystemTaskStatus.NOT_STARTED:
          default:
            stats.notStartedTasks++;
            break;
        }
      });
    });

    // Calculate overall completion percentage
    stats.overallCompletionPercentage = stats.totalTasks > 0
      ? Math.round((stats.completedTasks / stats.totalTasks) * 100)
      : 0;

    // Calculate task breakdown percentages
    Object.keys(stats.taskBreakdown).forEach(taskKey => {
      const breakdown = stats.taskBreakdown[taskKey];
      breakdown.percentage = breakdown.total > 0
        ? Math.round((breakdown.completed / breakdown.total) * 100)
        : 0;
    });

    return stats;
  };

  const stats = calculateStats();

  // Task management functions
  const handleStatusChange = async (systemId: string, taskType: TaskTypeKey, newStatus: SystemTaskStatus) => {
    try {
      await SystemsService.updateSystemTaskStatus(systemId, taskType, newStatus);
      await loadData(); // Reload data to reflect changes

      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم تحديث الحالة" : "Status Updated",
        description: isRTL ? "تم تحديث حالة المهمة بنجاح" : "Task status updated successfully",
      });
    } catch (error) {
      console.error('Error updating task status:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في التحديث" : "Update Error",
        description: isRTL ? "فشل في تحديث حالة المهمة" : "Failed to update task status",
        variant: "destructive",
      });
    }
  };

  const handleSaveNote = async () => {
    if (!notesModal) return;

    try {
      await SystemsService.updateSystemTaskStatus(
        notesModal.systemId,
        notesModal.taskType,
        systems.find(s => s.id === notesModal.systemId)?.[notesModal.taskType] as SystemTaskStatus,
        undefined,
        undefined,
        undefined,
        undefined,
        tempNote.trim()
      );

      setNotesModal(null);
      setTempNote('');
      await loadData();

      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم حفظ الملاحظة" : "Note Saved",
        description: isRTL ? "تم حفظ الملاحظة بنجاح" : "Note saved successfully",
      });
    } catch (error) {
      console.error('Error saving note:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: isRTL ? "فشل في حفظ الملاحظة" : "Failed to save note",
        variant: "destructive",
      });
    }
  };

  const handleLinkMeeting = async (meetingId: string) => {
    if (!meetingModal) return;

    try {
      await SystemsService.updateSystemTaskStatus(
        meetingModal.systemId,
        'classificationReviewStatus',
        SystemTaskStatus.MEETING_SCHEDULED,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        meetingId
      );

      setMeetingModal(null);
      await loadData();

      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم ربط الاجتماع" : "Meeting Linked",
        description: isRTL ? "تم ربط الاجتماع بنجاح" : "Meeting linked successfully",
      });
    } catch (error) {
      console.error('Error linking meeting:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في الربط" : "Link Error",
        description: isRTL ? "فشل في ربط الاجتماع" : "Failed to link meeting",
        variant: "destructive",
      });
    }
  };

  const handleUnlinkMeeting = async (systemId: string) => {
    try {
      await SystemsService.updateSystemTaskStatus(
        systemId,
        'classificationReviewStatus',
        SystemTaskStatus.NOT_STARTED,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        '' // Clear meeting ID
      );

      setMeetingDetailsModal(null);
      await loadData();

      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "تم إلغاء ربط الاجتماع" : "Meeting Unlinked",
        description: isRTL ? "تم إلغاء ربط الاجتماع بنجاح" : "Meeting unlinked successfully",
      });
    } catch (error) {
      console.error('Error unlinking meeting:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في إلغاء الربط" : "Unlink Error",
        description: isRTL ? "فشل في إلغاء ربط الاجتماع" : "Failed to unlink meeting",
        variant: "destructive",
      });
    }
  };

  const getStatusColor = (status: SystemTaskStatus): string => {
    switch (status) {
      case SystemTaskStatus.DONE:
        return 'bg-green-50 text-green-700 border-green-200';
      case SystemTaskStatus.IN_PROGRESS:
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case SystemTaskStatus.DATA_REQUESTED:
        return 'bg-orange-50 text-orange-700 border-orange-200';
      case SystemTaskStatus.DATA_RECEIVED:
        return 'bg-cyan-50 text-cyan-700 border-cyan-200';
      case SystemTaskStatus.DATA_SENT_AWAITING_REPLY:
        return 'bg-purple-50 text-purple-700 border-purple-200';
      case SystemTaskStatus.MEETING_SCHEDULED:
        return 'bg-indigo-50 text-indigo-700 border-indigo-200';
      case SystemTaskStatus.MEETING_DONE:
        return 'bg-teal-50 text-teal-700 border-teal-200';
      case SystemTaskStatus.NO_REPLY_RECEIVED:
        return 'bg-red-50 text-red-700 border-red-200';
      case SystemTaskStatus.APPROVAL_DELAYED:
        return 'bg-red-50 text-red-700 border-red-200';
      case SystemTaskStatus.NOT_STARTED:
      default:
        return 'bg-gray-50 text-gray-600 border-gray-200';
    }
  };

  const getStatusIcon = (status: SystemTaskStatus) => {
    switch (status) {
      case SystemTaskStatus.DONE:
        return <CheckCircle2 className="w-4 h-4" />;
      case SystemTaskStatus.IN_PROGRESS:
        return <Loader className="w-4 h-4" />;
      case SystemTaskStatus.DATA_REQUESTED:
        return <Mail className="w-4 h-4" />;
      case SystemTaskStatus.DATA_RECEIVED:
        return <CheckCircle className="w-4 h-4" />;
      case SystemTaskStatus.DATA_SENT_AWAITING_REPLY:
        return <Clock className="w-4 h-4" />;
      case SystemTaskStatus.MEETING_SCHEDULED:
        return <Calendar className="w-4 h-4" />;
      case SystemTaskStatus.MEETING_DONE:
        return <Users className="w-4 h-4" />;
      case SystemTaskStatus.NO_REPLY_RECEIVED:
        return <XCircle className="w-4 h-4" />;
      case SystemTaskStatus.APPROVAL_DELAYED:
        return <AlertTriangle className="w-4 h-4" />;
      case SystemTaskStatus.NOT_STARTED:
      default:
        return <Circle className="w-4 h-4" />;
    }
  };



  const formatDate = (timestamp?: Timestamp): string => {
    if (!timestamp) return '';
    return timestamp.toDate().toLocaleDateString(lang === 'ar' ? 'ar-SA' : 'en-US');
  };

  // Group systems by their group number
  const groupSystemsByGroup = () => {
    const grouped: { [key: string]: System[] } = {};

    // Initialize groups 1-9
    for (let i = 1; i <= 9; i++) {
      grouped[i.toString()] = [];
    }

    // Add a group for systems without a group
    grouped['unassigned'] = [];

    // Group systems
    systems.forEach(system => {
      if (system.group && system.group >= '1' && system.group <= '9') {
        grouped[system.group].push(system);
      } else {
        grouped['unassigned'].push(system);
      }
    });

    return grouped;
  };

  // Calculate group progress
  const calculateGroupProgress = (groupSystems: System[]) => {
    if (groupSystems.length === 0) return 0;

    const totalTasks = groupSystems.length * taskTypes.length;
    let completedTasks = 0;

    groupSystems.forEach(system => {
      taskTypes.forEach(taskType => {
        const status = system[taskType.key as keyof System] as SystemTaskStatus;
        if (status === SystemTaskStatus.DONE || status === SystemTaskStatus.DATA_RECEIVED) {
          completedTasks++;
        }
      });
    });

    return Math.round((completedTasks / totalTasks) * 100);
  };

  // Toggle group minimization
  const toggleGroupMinimization = (groupId: string) => {
    const newMinimized = new Set(minimizedGroups);
    if (newMinimized.has(groupId)) {
      newMinimized.delete(groupId);
    } else {
      newMinimized.add(groupId);
    }
    setMinimizedGroups(newMinimized);
  };

  // Handle drill-down to group details
  const handleGroupDrillDown = (groupId: string, groupName: string, systems: System[], progress: number) => {
    // Navigate to detailed group view
    router.push(`/${lang}/Thiqah/Tasks/Systems/Groups/${groupId}`);
  };

  // Handle group detail modal
  const handleGroupDetailClick = (groupId: string, groupName: string, systems: System[], progress: number) => {
    setGroupDetailModal({
      groupId,
      groupName,
      systems,
      progress
    });
  };

  // Handle link save
  const handleLinkSave = async () => {
    if (!linkModal) return;

    try {
      const linkFieldName = `${linkModal.taskType.replace('Status', 'Link')}` as keyof System;
      await SystemsService.updateSystem(linkModal.systemId, {
        [linkFieldName]: tempLink.trim() || null
      });

      // Refresh systems data
      const updatedSystems = await SystemsService.getSystems();
      setSystems(updatedSystems);

      toast({
        title: isRTL ? "تم حفظ الرابط" : "Link Saved",
        description: isRTL ? "تم حفظ الرابط بنجاح" : "Link has been saved successfully",
      });

      setLinkModal(null);
      setTempLink('');
    } catch (error) {
      console.error('Error saving link:', error);
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: isRTL ? "فشل في حفظ الرابط" : "Failed to save link",
        variant: "destructive",
      });
    }
  };

  // Handle date save
  const handleDateSave = async () => {
    if (!dateEditModal) return;

    try {
      let startDate: Date | undefined;
      let endDate: Date | undefined;

      if (dateEditModal.taskType === 'classificationDataStatus' || dateEditModal.taskType === 'finalApprovalStatus') {
        // Single date for Classification and Final Approval
        startDate = tempStartDate ? new Date(tempStartDate) : undefined;
      } else {
        // Start and end dates for other tasks
        startDate = tempStartDate ? new Date(tempStartDate) : undefined;
        endDate = tempEndDate ? new Date(tempEndDate) : undefined;
      }

      // Get current status, default to NOT_STARTED if undefined
      const currentSystem = systems.find(s => s.id === dateEditModal.systemId);
      const currentStatus = currentSystem?.[dateEditModal.taskType] ?? SystemTaskStatus.NOT_STARTED;

      await SystemsService.updateSystemTaskStatus(
        dateEditModal.systemId,
        dateEditModal.taskType,
        currentStatus as SystemTaskStatus,
        startDate,
        endDate
      );

      await loadData();

      toast({
        title: isRTL ? "تم حفظ التواريخ" : "Dates Saved",
        description: isRTL ? "تم حفظ التواريخ بنجاح" : "Dates have been saved successfully",
      });

      setDateEditModal(null);
      setTempStartDate('');
      setTempEndDate('');
    } catch (error) {
      console.error('Error saving dates:', error);
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: isRTL ? "فشل في حفظ التواريخ" : "Failed to save dates",
        variant: "destructive",
      });
    }
  };

  // Handle approval delay reason save
  const handleApprovalDelayReasonSave = async () => {
    if (!approvalDelayModal) return;

    try {
      // Add the delay entry to the system's delay history
      await SystemsService.addApprovalDelayEntry(
        approvalDelayModal.systemId,
        tempDelayReason.trim()
      );

      await loadData();

      toast({
        title: isRTL ? "تم إضافة التأخير" : "Delay Added to Timeline",
        description: isRTL ? "تم إضافة سبب التأخير إلى التاريخ بنجاح" : "Delay reason has been added to timeline successfully",
      });

      // Clear the input field but keep the modal open to show the updated timeline
      setTempDelayReason('');
    } catch (error) {
      console.error('Error saving approval delay reason:', error);
      toast({
        title: isRTL ? "خطأ في الحفظ" : "Save Error",
        description: isRTL ? "فشل في حفظ سبب التأخير" : "Failed to save delay reason",
        variant: "destructive",
      });
    }
  };

  // Handle delay entry edit save
  const handleDelayEntryEditSave = async () => {
    if (!editingDelayEntry || !approvalDelayModal) return;

    try {
      await SystemsService.updateApprovalDelayEntry(
        approvalDelayModal.systemId,
        editingDelayEntry.entryId,
        editingDelayEntry.reason.trim()
      );

      await loadData();

      toast({
        title: isRTL ? "تم تحديث التأخير" : "Delay Updated",
        description: isRTL ? "تم تحديث سبب التأخير بنجاح" : "Delay reason has been updated successfully",
      });

      setEditingDelayEntry(null);
    } catch (error) {
      console.error('Error updating delay entry:', error);
      toast({
        title: isRTL ? "خطأ في التحديث" : "Update Error",
        description: isRTL ? "فشل في تحديث سبب التأخير" : "Failed to update delay reason",
        variant: "destructive",
      });
    }
  };

  // Handle delay entry delete
  const handleDelayEntryDelete = async (systemId: string, entryId: string) => {
    try {
      await SystemsService.deleteApprovalDelayEntry(systemId, entryId);
      await loadData();

      toast({
        title: isRTL ? "تم حذف التأخير" : "Delay Deleted",
        description: isRTL ? "تم حذف سبب التأخير بنجاح" : "Delay reason has been deleted successfully",
      });

      setEditingDelayEntry(null);
    } catch (error) {
      console.error('Error deleting delay entry:', error);
      toast({
        title: isRTL ? "خطأ في الحذف" : "Delete Error",
        description: isRTL ? "فشل في حذف سبب التأخير" : "Failed to delete delay reason",
        variant: "destructive",
      });
    }
  };

  // Handle bulk update for group
  const handleBulkUpdate = async () => {
    if (!bulkUpdateModal) return;

    try {
      setIsBulkUpdating(true);

      // Determine dates based on task type
      let startDate: Date | undefined;
      let endDate: Date | undefined;

      if (bulkUpdateModal.taskType === 'classificationDataStatus' || bulkUpdateModal.taskType === 'finalApprovalStatus') {
        // Single date for Classification and Final Approval
        startDate = bulkStartDate ? new Date(bulkStartDate) : undefined;
      } else {
        // Start and end dates for other tasks
        startDate = bulkStartDate ? new Date(bulkStartDate) : undefined;
        endDate = bulkEndDate ? new Date(bulkEndDate) : undefined;
      }

      // Update all systems in the group
      const updatePromises = bulkUpdateModal.groupSystems.map(system =>
        SystemsService.updateSystemTaskStatus(
          system.id!,
          bulkUpdateModal.taskType,
          bulkStatus,
          startDate,
          endDate,
          undefined, // meetingId
          bulkLink.trim() || undefined,
          bulkNote.trim() || undefined
        )
      );

      await Promise.all(updatePromises);
      await loadData();

      toast({
        title: isRTL ? "تم التحديث المجمع" : "Bulk Update Complete",
        description: isRTL
          ? `تم تحديث ${bulkUpdateModal.groupSystems.length} نظام في المجموعة ${bulkUpdateModal.groupNumber}`
          : `Updated ${bulkUpdateModal.groupSystems.length} systems in Group ${bulkUpdateModal.groupNumber}`,
      });

      setBulkUpdateModal(null);
      setBulkStatus(SystemTaskStatus.NOT_STARTED);
      setBulkNote('');
      setBulkLink('');
      setBulkStartDate('');
      setBulkEndDate('');
    } catch (error) {
      console.error('Error performing bulk update:', error);
      toast({
        title: isRTL ? "خطأ في التحديث المجمع" : "Bulk Update Error",
        description: isRTL ? "فشل في تحديث الأنظمة" : "Failed to update systems",
        variant: "destructive",
      });
    } finally {
      setIsBulkUpdating(false);
    }
  };

  // Enhanced Excel Export with Group System and Gantt Chart
  const exportToExcel = async () => {
    try {
      setIsExporting(true);
      const workbook = new ExcelJS.Workbook();

      // Set workbook properties with Thiqah branding
      workbook.creator = 'Thiqah Business Services';
      workbook.lastModifiedBy = 'Thiqah Digital Solutions';
      workbook.created = new Date();
      workbook.modified = new Date();
      workbook.lastPrinted = new Date();
      workbook.company = 'Thiqah Business Services';
      workbook.manager = 'Thiqah Management';

      // EXACT Thiqah Brand Colors from globals.css
      const THIQAH_COLORS = {
        primary: 'FF23A9DB',      // --brand-blue: #23A9DB
        secondary: 'FF3D3D45',    // --brand-dark-gray: #3D3D45
        white: 'FFFFFFFF',        // --brand-white: #FFFFFF
        lightBlue: 'FFE8F4FD',    // Light variation of brand blue
        darkBlue: 'FF1E96C8',     // Darker variation of brand blue
        accent: 'FF23A9DB',       // Same as primary for consistency
        success: 'FF10B981',      // Success Green
        warning: 'FFF59E0B',      // Warning Orange
        danger: 'FFEF4444',       // Danger Red
        gray: 'FF6B7280',         // Neutral Gray
        group1: 'FF8B5CF6',       // Purple for Group 1
        group2: 'FF06B6D4',       // Cyan for Group 2
        group3: 'FF10B981',       // Green for Group 3
        group4: 'FFF59E0B',       // Orange for Group 4
        group5: 'FFEF4444',       // Red for Group 5
        group6: 'FF8B5CF6',       // Purple for Group 6
        group7: 'FF06B6D4',       // Cyan for Group 7
        group8: 'FF10B981',       // Green for Group 8
        group9: 'FFF59E0B',       // Orange for Group 9
      };

      // Get grouped systems data
      const groupedSystems = groupSystemsByGroup();

      // ==================== PROFESSIONAL THIQAH EXECUTIVE SUMMARY ====================
      const summarySheet = workbook.addWorksheet(isRTL ? 'الملخص التنفيذي' : 'Executive Summary');
      summarySheet.columns = Array(10).fill({ width: 15 }); // Expanded for better content display

      // PROFESSIONAL TITLE with pure Thiqah blue
      summarySheet.mergeCells('A1:J2');
      const summaryTitle = summarySheet.getCell('A1');
      summaryTitle.value = isRTL ? 'الملخص التنفيذي - لوحة القيادة الذكية' : 'EXECUTIVE SUMMARY - SMART DASHBOARD';
      summaryTitle.font = { size: 20, bold: true, color: { argb: THIQAH_COLORS.white } };
      summaryTitle.alignment = { horizontal: 'center', vertical: 'middle' };
      summaryTitle.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: THIQAH_COLORS.primary } // Pure #23A9DB only
      };
      summaryTitle.border = {
        top: { style: 'thick', color: { argb: THIQAH_COLORS.secondary } },
        left: { style: 'thick', color: { argb: THIQAH_COLORS.secondary } },
        bottom: { style: 'thick', color: { argb: THIQAH_COLORS.secondary } },
        right: { style: 'thick', color: { argb: THIQAH_COLORS.secondary } }
      };

      // PROFESSIONAL Statistics Cards with Thiqah colors
      const stats = calculateStats();
      const statsCards = [
        {
          label: isRTL ? 'إجمالي الأنظمة' : 'Total Systems',
          value: stats.totalSystems,
          color: THIQAH_COLORS.primary,
          bgColor: THIQAH_COLORS.lightBlue
        },
        {
          label: isRTL ? 'الأنظمة المكتملة' : 'Completed Systems',
          value: stats.completedSystems,
          color: THIQAH_COLORS.success,
          bgColor: 'FFD1FAE5'
        },
        {
          label: isRTL ? 'المهام المعلقة' : 'Pending Tasks',
          value: stats.pendingTasks,
          color: THIQAH_COLORS.warning,
          bgColor: 'FFFEF3C7'
        },
        {
          label: isRTL ? 'المهام غير المبدوءة' : 'Not Started',
          value: stats.notStartedTasks,
          color: THIQAH_COLORS.danger,
          bgColor: 'FFFECACA'
        }
      ];

      // EXPANDED KPI Cards Layout - 2x2 grid with proper sizing
      statsCards.forEach((card, index) => {
        const row = Math.floor(index / 2);
        const col = index % 2;
        const startRow = 4 + (row * 4); // Increased spacing between rows
        const endRow = startRow + 3; // Taller cards
        const startCol = 1 + (col * 5); // More spacing between columns
        const endCol = startCol + 4; // Wider cards

        // EXPANDED KPI Card with exact Thiqah brand colors
        summarySheet.mergeCells(`${String.fromCharCode(64 + startCol)}${startRow}:${String.fromCharCode(64 + endCol)}${endRow}`);
        const cardCell = summarySheet.getCell(`${String.fromCharCode(64 + startCol)}${startRow}`);
        cardCell.value = `${card.label}\n\n${card.value}`;
        cardCell.font = { size: 16, bold: true, color: { argb: THIQAH_COLORS.white } };
        cardCell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

        // PURE THIQAH BLUE - SAME AS COLUMN HEADERS
        cardCell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: THIQAH_COLORS.primary } // Pure #23A9DB only - SAME AS HEADERS
        };
        cardCell.border = {
          top: { style: 'thick', color: { argb: THIQAH_COLORS.secondary } },
          left: { style: 'thick', color: { argb: THIQAH_COLORS.secondary } },
          bottom: { style: 'thick', color: { argb: THIQAH_COLORS.secondary } },
          right: { style: 'thick', color: { argb: THIQAH_COLORS.secondary } }
        };
      });

      // PROFESSIONAL Progress Overview with Thiqah styling (moved down for expanded KPIs)
      summarySheet.mergeCells('A13:J14');
      const progressTitle = summarySheet.getCell('A13');
      progressTitle.value = isRTL ? 'نظرة عامة على التقدم - تحليل متقدم' : 'PROGRESS OVERVIEW - ADVANCED ANALYTICS';
      progressTitle.font = { size: 16, bold: true, color: { argb: THIQAH_COLORS.white } };
      progressTitle.alignment = { horizontal: 'center', vertical: 'middle' };
      progressTitle.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: THIQAH_COLORS.primary } };
      progressTitle.border = {
        top: { style: 'thick', color: { argb: THIQAH_COLORS.primary } },
        left: { style: 'thick', color: { argb: THIQAH_COLORS.primary } },
        bottom: { style: 'thick', color: { argb: THIQAH_COLORS.primary } },
        right: { style: 'thick', color: { argb: THIQAH_COLORS.primary } }
      };

      // Headers for progress table
      const progressHeaders = [
        isRTL ? 'نوع المهمة' : 'Task Type',
        isRTL ? 'الإنجاز' : 'Progress',
        isRTL ? 'النسبة' : 'Percentage',
        isRTL ? 'المؤشر المرئي' : 'Visual Indicator'
      ];

      progressHeaders.forEach((header, index) => {
        const cell = summarySheet.getCell(`${String.fromCharCode(65 + index)}16`);
        cell.value = header;
        cell.font = { size: 12, bold: true, color: { argb: THIQAH_COLORS.white } };
        cell.alignment = { horizontal: 'center', vertical: 'middle' };

        // PURE Thiqah brand blue for progress headers
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: THIQAH_COLORS.primary } // Pure #23A9DB only
        };
        cell.border = {
          top: { style: 'medium', color: { argb: THIQAH_COLORS.secondary } },
          left: { style: 'medium', color: { argb: THIQAH_COLORS.secondary } },
          bottom: { style: 'medium', color: { argb: THIQAH_COLORS.secondary } },
          right: { style: 'medium', color: { argb: THIQAH_COLORS.secondary } }
        };
      });

      // PROFESSIONAL Task Types Progress with enhanced styling
      taskTypes.forEach((taskType, index) => {
        const rowNum = 17 + index;
        const completedCount = systems.filter(system => {
          const status = system[taskType.key as keyof System] as SystemTaskStatus;
          // For Classification Data task, treat DATA_RECEIVED as completed
          if (taskType.key === 'classificationDataStatus') {
            return status === SystemTaskStatus.DONE || status === SystemTaskStatus.DATA_RECEIVED;
          }
          return status === SystemTaskStatus.DONE;
        }).length;
        const progressPercentage = Math.round((completedCount / systems.length) * 100);

        // Task name - professional
        const taskCell = summarySheet.getCell(`A${rowNum}`);
        taskCell.value = taskType.label[lang as 'en' | 'ar'];
        taskCell.font = { size: 11, bold: true, color: { argb: THIQAH_COLORS.secondary } };
        taskCell.alignment = { horizontal: 'left', vertical: 'middle' };

        // Progress count
        const countCell = summarySheet.getCell(`B${rowNum}`);
        countCell.value = `${completedCount}/${systems.length}`;
        countCell.font = { size: 11, bold: true, color: { argb: THIQAH_COLORS.primary } };
        countCell.alignment = { horizontal: 'center', vertical: 'middle' };

        // Percentage
        const percentCell = summarySheet.getCell(`C${rowNum}`);
        percentCell.value = `${progressPercentage}%`;
        percentCell.font = {
          size: 12,
          bold: true,
          color: {
            argb: progressPercentage >= 80 ? THIQAH_COLORS.success :
                  progressPercentage >= 50 ? THIQAH_COLORS.warning :
                  THIQAH_COLORS.danger
          }
        };
        percentCell.alignment = { horizontal: 'center', vertical: 'middle' };

        // PROFESSIONAL Progress bar with Thiqah colors
        summarySheet.mergeCells(`D${rowNum}:G${rowNum}`);
        const progressBar = summarySheet.getCell(`D${rowNum}`);
        const filledBars = Math.floor(progressPercentage / 10);
        const emptyBars = 10 - filledBars;
        progressBar.value = '█'.repeat(filledBars) + '░'.repeat(emptyBars) + ` ${progressPercentage}%`;
        progressBar.font = { size: 10, bold: true, color: { argb: THIQAH_COLORS.primary } };
        progressBar.alignment = { horizontal: 'center', vertical: 'middle' };

        // Row styling with alternating colors
        [taskCell, countCell, percentCell, progressBar].forEach(cell => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: index % 2 === 0 ? THIQAH_COLORS.lightBlue : THIQAH_COLORS.white }
          };
          cell.border = {
            top: { style: 'thin', color: { argb: THIQAH_COLORS.gray } },
            left: { style: 'thin', color: { argb: THIQAH_COLORS.gray } },
            bottom: { style: 'thin', color: { argb: THIQAH_COLORS.gray } },
            right: { style: 'thin', color: { argb: THIQAH_COLORS.gray } }
          };
        });
      });







      // ==================== PROFESSIONAL DETAILED SYSTEMS DATA ====================
      const dataSheet = workbook.addWorksheet(isRTL ? 'بيانات الأنظمة التفصيلية' : 'Detailed Systems Data');

      // PROFESSIONAL headers with Thiqah organization including Group (REMOVED System Scope and Overall Status)
      const headers = [
        isRTL ? 'المجموعة' : 'Group',
        isRTL ? 'رقم النظام' : 'System #',
        isRTL ? 'اسم النظام' : 'System Name',
        isRTL ? 'المسؤول' : 'Owner',
        isRTL ? 'البريد الإلكتروني' : 'Email',
        isRTL ? 'المستشار المسؤول' : 'Assigned Consultant',
        isRTL ? 'نسبة الإنجاز' : 'Progress %'
      ];

      // Add PROFESSIONAL task status columns
      taskTypes.forEach(task => {
        headers.push(task.label[lang as 'en' | 'ar']);
      });

      // Add PROFESSIONAL date columns (single date for each task)
      taskTypes.forEach(task => {
        headers.push(`${task.label[lang as 'en' | 'ar']} - ${isRTL ? 'التاريخ' : 'Date'}`);
      });

      // Add PROFESSIONAL notes column
      headers.push(isRTL ? 'الملاحظات الشاملة' : 'Comprehensive Notes');

      dataSheet.addRow(headers);

      // PURE THIQAH BRAND BLUE for all column headers with WRAP TEXT
      const headerRow = dataSheet.getRow(1);
      headerRow.height = 60;
      headerRow.eachCell((cell) => {
        cell.font = { bold: true, color: { argb: THIQAH_COLORS.white }, size: 12 };

        // PURE Thiqah brand blue - NO gradients, NO dark blue
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: THIQAH_COLORS.primary } // Pure #23A9DB only
        };

        // WRAP TEXT for all headers
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        cell.border = {
          top: { style: 'thick', color: { argb: THIQAH_COLORS.secondary } },
          left: { style: 'thin', color: { argb: THIQAH_COLORS.secondary } },
          bottom: { style: 'thick', color: { argb: THIQAH_COLORS.secondary } },
          right: { style: 'thin', color: { argb: THIQAH_COLORS.secondary } }
        };
      });

      // Add system data organized by groups with enhanced formatting
      let systemCounter = 1;

      // Process groups 1-9 first
      for (let groupNum = 1; groupNum <= 9; groupNum++) {
        const groupSystems = groupedSystems[groupNum.toString()];
        if (groupSystems.length > 0) {
          // Add group header row (UPDATED for removed columns and added consultant)
          const groupHeaderRow = [
            `${isRTL ? 'المجموعة' : 'GROUP'} ${groupNum}`,
            '', '', '', '', '', '' // Removed 2 columns (System Scope and Overall Status), added 1 (Consultant)
          ];
          // Add empty cells for task columns
          taskTypes.forEach(() => groupHeaderRow.push(''));
          // Add empty cells for task links columns
          taskTypes.forEach(task => {
            if (task.hasLink) {
              groupHeaderRow.push('');
            }
          });
          // Add empty cells for date columns
          taskTypes.forEach(task => {
            if (task.key === 'classificationDataStatus' || task.key === 'finalApprovalStatus') {
              // Single date column
              groupHeaderRow.push('');
            } else {
              // Start and end date columns
              groupHeaderRow.push('');
              groupHeaderRow.push('');
            }
          });
          groupHeaderRow.push('');

          const groupRow = dataSheet.addRow(groupHeaderRow);
          const groupColor = THIQAH_COLORS[`group${groupNum}` as keyof typeof THIQAH_COLORS] || THIQAH_COLORS.gray;

          groupRow.eachCell((cell) => {
            cell.font = { bold: true, color: { argb: THIQAH_COLORS.white }, size: 14 };
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: groupColor } };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
          });

          // Add systems in this group
          groupSystems.forEach((system) => {
            const completedTasks = taskTypes.filter(task => {
              const status = system[task.key as keyof System] as SystemTaskStatus;
              return status === SystemTaskStatus.DONE || status === SystemTaskStatus.DATA_RECEIVED;
            }).length;
            const progressPercentage = Math.round((completedTasks / taskTypes.length) * 100);

            // UPDATED row data (REMOVED System Scope and Overall Status columns)
            const rowData = [
              `${isRTL ? 'مج' : 'G'}${groupNum}`,
              `#${String(systemCounter).padStart(3, '0')}`,
              system.name,
              system.responsibleOwner,
              system.email,
              system.consultantName || (isRTL ? 'غير محدد' : 'Not Assigned'),
              `${progressPercentage}%`
            ];

            // Add task statuses
            taskTypes.forEach(task => {
              const status = system[task.key as keyof System] as SystemTaskStatus || SystemTaskStatus.NOT_STARTED;
              const statusConfig = task.statuses.find(s => s.value === status);
              const statusText = statusConfig?.label[lang as 'en' | 'ar'] || status;
              rowData.push(statusText);
            });

            // Add dates (single date for each task - prefer end date if available, otherwise start date)
            taskTypes.forEach(task => {
              const startDate = system[`${task.key.replace('Status', 'StartDate')}` as keyof System] as Timestamp;
              const endDate = system[`${task.key.replace('Status', 'EndDate')}` as keyof System] as Timestamp;

              // Use end date if available, otherwise start date
              const dateToUse = endDate || startDate;
              rowData.push(dateToUse ? formatDate(dateToUse) : '');
            });

            // Add comprehensive notes with special handling for delay reasons
            const allNotes = taskTypes.map(task => {
              const note = system[`${task.key.replace('Status', 'Note')}` as keyof System] as string;
              if (note) {
                // Special handling for Final Approval delay reasons
                if (task.key === 'finalApprovalStatus' && system.finalApprovalDelayHistory && system.finalApprovalDelayHistory.length > 0) {
                  const delayCount = system.finalApprovalDelayHistory.length;
                  return `${task.label[lang as 'en' | 'ar']}: ${delayCount} ${isRTL ? 'تأخير' : 'delay'}${delayCount > 1 ? 's' : ''} - ${note}`;
                }
                return `${task.label[lang as 'en' | 'ar']}: ${note}`;
              }
              return '';
            }).filter(note => note).join(' | ');
            rowData.push(allNotes);

            const systemRow = dataSheet.addRow(rowData);

            // Style system rows with group color accent and COLOR CODING for status
            systemRow.eachCell((cell, colNumber) => {
              if (colNumber === 1) { // Group column
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: groupColor } };
                cell.font = { bold: true, color: { argb: THIQAH_COLORS.white } };
              } else {
                // COLOR CODING for task status columns
                const taskStatusStartCol = 7; // After Group, System#, Name, Owner, Email, Consultant, Progress%
                const taskStatusEndCol = taskStatusStartCol + taskTypes.length - 1;

                // Date columns start after status columns
                const dateColumnsStartCol = taskStatusEndCol + 1;
                const dateColumnsEndCol = dateColumnsStartCol + taskTypes.length - 1;

                // Comprehensive notes column is the last column
                const notesCol = dateColumnsEndCol + 1;

                if (colNumber >= taskStatusStartCol && colNumber <= taskStatusEndCol) {
                  // This is a task status column - apply color coding
                  const taskIndex = colNumber - taskStatusStartCol;
                  const taskType = taskTypes[taskIndex];
                  const status = system[taskType.key as keyof System] as SystemTaskStatus || SystemTaskStatus.NOT_STARTED;

                  let statusColor = THIQAH_COLORS.white; // Default

                  if (status === SystemTaskStatus.DATA_RECEIVED || status === SystemTaskStatus.DONE) {
                    statusColor = 'FFD1FAE5'; // Light green for completed
                  } else if (status === SystemTaskStatus.APPROVAL_DELAYED) {
                    statusColor = 'FFFECACA'; // Light red for approval delayed
                  } else if (status === SystemTaskStatus.NOT_STARTED) {
                    statusColor = 'FFFDE68A'; // Light yellow for not started
                  } else if (status === SystemTaskStatus.IN_PROGRESS || status === SystemTaskStatus.DATA_REQUESTED) {
                    statusColor = 'FFFEF3C7'; // Light yellow for in progress
                  } else if (status === SystemTaskStatus.DATA_SENT_AWAITING_REPLY || status === SystemTaskStatus.MEETING_SCHEDULED) {
                    statusColor = 'FFE0E7FF'; // Light blue for awaiting/scheduled
                  } else if (status === SystemTaskStatus.NO_REPLY_RECEIVED) {
                    statusColor = 'FFFECACA'; // Light red for no reply
                  }

                  cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: statusColor } };
                } else if (colNumber === notesCol) {
                  // Color coding for comprehensive notes column
                  const hasDelayReasons = system.finalApprovalDelayHistory && system.finalApprovalDelayHistory.length > 0;
                  const hasAnyNotes = taskTypes.some(task => {
                    const note = system[`${task.key.replace('Status', 'Note')}` as keyof System] as string;
                    return note && note.trim().length > 0;
                  });

                  if (hasDelayReasons) {
                    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFECACA' } }; // Light red for delay reasons
                  } else if (hasAnyNotes) {
                    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFEF3C7' } }; // Light yellow for other notes
                  } else {
                    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: systemCounter % 2 === 0 ? THIQAH_COLORS.lightBlue : THIQAH_COLORS.white } };
                  }
                } else {
                  cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: systemCounter % 2 === 0 ? THIQAH_COLORS.lightBlue : THIQAH_COLORS.white } };
                }
              }
              // WRAP TEXT for all cells
              cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });

            systemCounter++;
          });
        }
      }

      // Add unassigned systems if any
      const unassignedSystems = groupedSystems['unassigned'];
      if (unassignedSystems.length > 0) {
        // Add unassigned header row
        const unassignedHeaderRow = [
          isRTL ? 'بدون مجموعة' : 'UNASSIGNED',
          '', '', '', '', '', '' // Group, System#, Name, Owner, Email, Consultant, Progress%
        ];
        // Add empty cells for task status columns
        taskTypes.forEach(() => unassignedHeaderRow.push(''));
        // Add empty cells for date columns (one per task)
        taskTypes.forEach(() => unassignedHeaderRow.push(''));
        // Add empty cell for comprehensive notes
        unassignedHeaderRow.push('');

        const unassignedRow = dataSheet.addRow(unassignedHeaderRow);
        unassignedRow.eachCell((cell) => {
          cell.font = { bold: true, color: { argb: THIQAH_COLORS.white }, size: 14 };
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: THIQAH_COLORS.gray } };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });

        // Add unassigned systems
        unassignedSystems.forEach((system) => {
          const completedTasks = taskTypes.filter(task => {
            const status = system[task.key as keyof System] as SystemTaskStatus;
            return status === SystemTaskStatus.DONE || status === SystemTaskStatus.DATA_RECEIVED;
          }).length;
          const progressPercentage = Math.round((completedTasks / taskTypes.length) * 100);

          // UPDATED row data (REMOVED System Scope and Overall Status columns)
          const rowData = [
            isRTL ? 'بدون' : 'N/A',
            `#${String(systemCounter).padStart(3, '0')}`,
            system.name,
            system.responsibleOwner,
            system.email,
            system.consultantName || (isRTL ? 'غير محدد' : 'Not Assigned'),
            `${progressPercentage}%`
          ];

          // Add task statuses
          taskTypes.forEach(task => {
            const status = system[task.key as keyof System] as SystemTaskStatus || SystemTaskStatus.NOT_STARTED;
            const statusConfig = task.statuses.find(s => s.value === status);
            const statusText = statusConfig?.label[lang as 'en' | 'ar'] || status;
            rowData.push(statusText);
          });

          // Add dates (single date for each task - prefer end date if available, otherwise start date)
          taskTypes.forEach(task => {
            const startDate = system[`${task.key.replace('Status', 'StartDate')}` as keyof System] as Timestamp;
            const endDate = system[`${task.key.replace('Status', 'EndDate')}` as keyof System] as Timestamp;

            // Use end date if available, otherwise start date
            const dateToUse = endDate || startDate;
            rowData.push(dateToUse ? formatDate(dateToUse) : '');
          });

          // Add comprehensive notes with special handling for delay reasons
          const allNotes = taskTypes.map(task => {
            const note = system[`${task.key.replace('Status', 'Note')}` as keyof System] as string;
            if (note) {
              // Special handling for Final Approval delay reasons
              if (task.key === 'finalApprovalStatus' && system.finalApprovalDelayHistory && system.finalApprovalDelayHistory.length > 0) {
                const delayCount = system.finalApprovalDelayHistory.length;
                return `${task.label[lang as 'en' | 'ar']}: ${delayCount} ${isRTL ? 'تأخير' : 'delay'}${delayCount > 1 ? 's' : ''} - ${note}`;
              }
              return `${task.label[lang as 'en' | 'ar']}: ${note}`;
            }
            return '';
          }).filter(note => note).join(' | ');
          rowData.push(allNotes);

          const systemRow = dataSheet.addRow(rowData);
          systemRow.eachCell((cell, colNumber) => {
            if (colNumber === 1) { // Group column
              cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: THIQAH_COLORS.gray } };
              cell.font = { bold: true, color: { argb: THIQAH_COLORS.white } };
            } else {
              // COLOR CODING for task status columns and notes (same as grouped systems)
              const taskStatusStartCol = 7; // After Group, System#, Name, Owner, Email, Consultant, Progress%
              const taskStatusEndCol = taskStatusStartCol + taskTypes.length - 1;

              // Date columns start after status columns
              const dateColumnsStartCol = taskStatusEndCol + 1;
              const dateColumnsEndCol = dateColumnsStartCol + taskTypes.length - 1;

              // Comprehensive notes column is the last column
              const notesCol = dateColumnsEndCol + 1;

              if (colNumber >= taskStatusStartCol && colNumber <= taskStatusEndCol) {
                // This is a task status column - apply color coding
                const taskIndex = colNumber - taskStatusStartCol;
                const taskType = taskTypes[taskIndex];
                const status = system[taskType.key as keyof System] as SystemTaskStatus || SystemTaskStatus.NOT_STARTED;

                let statusColor = THIQAH_COLORS.white; // Default

                if (status === SystemTaskStatus.DATA_RECEIVED || status === SystemTaskStatus.DONE) {
                  statusColor = 'FFD1FAE5'; // Light green for completed
                } else if (status === SystemTaskStatus.APPROVAL_DELAYED) {
                  statusColor = 'FFFECACA'; // Light red for approval delayed
                } else if (status === SystemTaskStatus.NOT_STARTED) {
                  statusColor = 'FFFDE68A'; // Light yellow for not started
                } else if (status === SystemTaskStatus.IN_PROGRESS || status === SystemTaskStatus.DATA_REQUESTED) {
                  statusColor = 'FFFEF3C7'; // Light yellow for in progress
                } else if (status === SystemTaskStatus.DATA_SENT_AWAITING_REPLY || status === SystemTaskStatus.MEETING_SCHEDULED) {
                  statusColor = 'FFE0E7FF'; // Light blue for awaiting/scheduled
                } else if (status === SystemTaskStatus.NO_REPLY_RECEIVED) {
                  statusColor = 'FFFECACA'; // Light red for no reply
                }

                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: statusColor } };
              } else if (colNumber === notesCol) {
                // Color coding for comprehensive notes column
                const hasDelayReasons = system.finalApprovalDelayHistory && system.finalApprovalDelayHistory.length > 0;
                const hasAnyNotes = taskTypes.some(task => {
                  const note = system[`${task.key.replace('Status', 'Note')}` as keyof System] as string;
                  return note && note.trim().length > 0;
                });

                if (hasDelayReasons) {
                  cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFECACA' } }; // Light red for delay reasons
                } else if (hasAnyNotes) {
                  cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFEF3C7' } }; // Light yellow for other notes
                } else {
                  cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: systemCounter % 2 === 0 ? THIQAH_COLORS.lightBlue : THIQAH_COLORS.white } };
                }
              } else {
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: systemCounter % 2 === 0 ? THIQAH_COLORS.lightBlue : THIQAH_COLORS.white } };
              }
            }
            // WRAP TEXT for all cells
            cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          });

          systemCounter++;
        });
      }

      // PROFESSIONAL column sizing for perfect cell expansion
      dataSheet.columns.forEach((column, index) => {
        if (index === 0) column.width = 12;  // Group - wider for group info
        else if (index === 1) column.width = 10; // System # - adequate for numbering
        else if (index === 2) column.width = 30; // System Name - expanded for full names
        else if (index === 3) column.width = 25; // Owner - expanded for full names
        else if (index === 4) column.width = 35; // Email - expanded for full email addresses
        else if (index === 5) column.width = 25; // Consultant - expanded for consultant names
        else if (index === 6) column.width = 15; // Progress - adequate for percentages
        else if (index <= 6 + taskTypes.length) column.width = 20; // Task status columns - expanded
        else if (index <= 6 + taskTypes.length + taskTypes.length) column.width = 18; // Date columns - one per task
        else column.width = 50; // Notes column - very wide for comprehensive notes with delay info
      });

      // Add freeze panes for better navigation (updated for group column)
      dataSheet.views = [{ state: 'frozen', xSplit: 3, ySplit: 1 }];

      // Add Color Coding Legend

      // Legend Title
      const legendTitleRow = dataSheet.addRow([]);
      legendTitleRow.getCell(1).value = isRTL ? 'دليل الألوان:' : 'Color Legend:';
      legendTitleRow.getCell(1).font = { bold: true, size: 14 };
      legendTitleRow.getCell(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: THIQAH_COLORS.primary } };
      legendTitleRow.getCell(1).font.color = { argb: THIQAH_COLORS.white };

      // Status Colors Legend
      const statusLegendItems = [
        { color: 'FFD1FAE5', label: isRTL ? 'مكتمل (تم الانتهاء / تم استلام البيانات)' : 'Completed (Done / Data Received)' },
        { color: 'FFFECACA', label: isRTL ? 'تأخير في الموافقة / لا يوجد رد' : 'Approval Delayed / No Reply' },
        { color: 'FFFDE68A', label: isRTL ? 'لم يبدأ' : 'Not Started' },
        { color: 'FFFEF3C7', label: isRTL ? 'قيد التنفيذ / تم طلب البيانات' : 'In Progress / Data Requested' },
        { color: 'FFE0E7FF', label: isRTL ? 'في انتظار الرد / تم جدولة الاجتماع' : 'Awaiting Reply / Meeting Scheduled' }
      ];

      statusLegendItems.forEach((item) => {
        const legendRow = dataSheet.addRow([]);
        legendRow.getCell(1).value = '■';
        legendRow.getCell(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: item.color } };
        legendRow.getCell(1).font = { size: 16, bold: true };
        legendRow.getCell(2).value = item.label;
        legendRow.getCell(2).font = { size: 11 };
      });

      // Notes Colors Legend
      dataSheet.addRow([]); // Empty row
      const notesLegendTitleRow = dataSheet.addRow([]);
      notesLegendTitleRow.getCell(1).value = isRTL ? 'ألوان الملاحظات الشاملة:' : 'Comprehensive Notes Colors:';
      notesLegendTitleRow.getCell(1).font = { bold: true, size: 12 };

      const notesLegendItems = [
        { color: 'FFFECACA', label: isRTL ? 'يحتوي على أسباب تأخير الموافقة' : 'Contains Approval Delay Reasons' },
        { color: 'FFFEF3C7', label: isRTL ? 'يحتوي على ملاحظات أخرى' : 'Contains Other Notes' }
      ];

      notesLegendItems.forEach((item) => {
        const legendRow = dataSheet.addRow([]);
        legendRow.getCell(1).value = '■';
        legendRow.getCell(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: item.color } };
        legendRow.getCell(1).font = { size: 16, bold: true };
        legendRow.getCell(2).value = item.label;
        legendRow.getCell(2).font = { size: 11 };
      });

      // Generate and download file with enhanced group-based naming
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      link.download = `Thiqah_Group_Based_System_Tasks_Report_${timestamp}.xlsx`;
      link.click();
      window.URL.revokeObjectURL(url);

      toast({
        title: isRTL ? "تم تصدير التقرير المحسن بنجاح" : "Enhanced Report Exported Successfully",
        description: isRTL ? "تم إنشاء تقرير شامل مع مخطط جانت تفصيلي وبيانات ملونة" : "Comprehensive report with detailed Gantt chart and color-coded data generated",
      });
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: isRTL ? "خطأ في التصدير" : "Export Error",
        description: isRTL ? "فشل في تصدير التقرير" : "Failed to export report",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white/20 rounded-full"></div>
          <div className="absolute bottom-1/4 right-1/3 w-20 h-20 bg-white/15 rounded-full"></div>
        </div>

        <div className="relative z-10 px-8 py-8">
          {/* Back Button */}
          <div className="mb-6">
            <Button
              onClick={() => router.push(`/${lang}/Thiqah/Tasks`)}
              variant="ghost"
              className="text-white hover:bg-white/20 rounded-xl"
            >
              <ArrowLeft className={`w-4 h-4 ${isRTL ? 'ml-2 rotate-180' : 'mr-2'}`} />
              {isRTL ? "العودة للمهام" : "Back to Tasks"}
            </Button>
          </div>

          {/* System Header */}
          <div className="flex items-center gap-6 mb-6">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center shadow-lg border border-white/20">
              <Activity className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-1 tracking-tight">
                {isRTL ? "حالة مهام الأنظمة" : "System Tasks Status"}
              </h1>
              <p className="text-white/90 text-lg">
                {isRTL ? "متابعة وإدارة حالة المهام لجميع الأنظمة" : "Monitor and manage task status across all systems"}
              </p>
            </div>
          </div>

          {/* Status Info Cards */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
            {[
              {
                icon: Database,
                label: isRTL ? "إجمالي الأنظمة" : "Total Systems",
                value: isLoading ? "..." : stats.totalSystems.toString(),
                subValue: isRTL ? "نظام مسجل" : "Registered Systems",
                color: "bg-blue-500/20",
                clickable: false
              },
              {
                icon: CheckCircle,
                label: isRTL ? "الأنظمة المكتملة" : "Completed Systems",
                value: isLoading ? "..." : stats.completedSystems.toString(),
                subValue: isRTL ? "نظام مكتمل" : "Systems Done",
                color: "bg-green-500/20",
                clickable: false
              },
              {
                icon: Clock,
                label: isRTL ? "المهام المعلقة" : "Pending Tasks",
                value: isLoading ? "..." : stats.pendingTasks.toString(),
                subValue: isRTL ? "مهمة معلقة" : "Tasks Pending",
                color: "bg-yellow-500/20",
                clickable: false
              },
              {
                icon: AlertTriangle,
                label: isRTL ? "المهام غير المبدوءة" : "Not Started",
                value: isLoading ? "..." : stats.notStartedTasks.toString(),
                subValue: isRTL ? "مهمة لم تبدأ" : "Tasks Not Started",
                color: "bg-red-500/20",
                clickable: false
              },
              {
                icon: TrendingUp,
                label: isRTL ? "نسبة الإنجاز الإجمالية" : "Overall Completion",
                value: isLoading ? "..." : `${stats.overallCompletionPercentage}%`,
                subValue: isRTL ? "انقر للتفاصيل" : "Click for Details",
                color: "bg-purple-500/20",
                clickable: true
              }
            ].map((item, index) => (
              <div
                key={index}
                className={`bg-white/10 backdrop-blur-xl rounded-lg p-3 border border-white/20 ${
                  item.clickable ? 'cursor-pointer hover:bg-white/20 transition-all duration-200 hover:scale-105' : ''
                }`}
                onClick={item.clickable ? () => setProgressBreakdownModal(true) : undefined}
              >
                <div className="flex items-center gap-2">
                  <div className={`p-1.5 rounded-lg ${item.color} text-white`}>
                    <item.icon className="w-3 h-3" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="text-xs font-semibold text-white/80 uppercase tracking-wider">{item.label}</div>
                    <div className="text-sm font-bold text-white truncate" title={item.value}>{item.value}</div>
                    {item.clickable && (
                      <div className="text-xs text-white/60 mt-1">{item.subValue}</div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Journey Navigation Section */}
      <div className="bg-white border-b border-gray-200 px-8 py-8">
        <SystemTasksJourney
          lang={lang}
          phaseStatus={{
            dataClassification: {
              status: 'current',
              completionRate: Math.round(stats.overallCompletionPercentage)
            },
            ropa: {
              status: stats.overallCompletionPercentage === 100 ? 'current' : 'locked',
              completionRate: 0
            },
            dpiaAndTia: {
              status: 'locked',
              completionRate: 0
            },
            pdplGovernance: {
              status: 'locked',
              completionRate: 0
            },
            knowledgeTransfer: {
              status: 'locked',
              completionRate: 0
            }
          }}
          currentPhase="dataClassification"
          showNavigation={false}
        />
      </div>

      {/* Tabs Section */}
      <div className="px-6 py-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger
              value="dashboard"
              className="flex items-center gap-2 data-[state=active]:bg-[var(--brand-blue)] data-[state=active]:text-white"
            >
              <PieChart size={16} />
              {isRTL ? "لوحة التحكم" : "Dashboard"}
            </TabsTrigger>
            <TabsTrigger
              value="status"
              className="flex items-center gap-2 data-[state=active]:bg-[var(--brand-blue)] data-[state=active]:text-white"
            >
              <Target size={16} />
              {isRTL ? "حالة مهام الأنظمة" : "System Task Status"}
            </TabsTrigger>
          </TabsList>

          {/* Dashboard Tab */}
          <TabsContent value="dashboard">
            <div className="space-y-6">
              {/* KPI Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                {[
                  {
                    title: isRTL ? "إجمالي الأنظمة" : "Total Systems",
                    value: stats.totalSystems,
                    icon: Database,
                    color: "bg-blue-500",
                    bgColor: "bg-blue-50",
                    textColor: "text-blue-600"
                  },
                  {
                    title: isRTL ? "الأنظمة المكتملة" : "Completed Systems",
                    value: stats.completedSystems,
                    icon: CheckCircle,
                    color: "bg-green-500",
                    bgColor: "bg-green-50",
                    textColor: "text-green-600"
                  },
                  {
                    title: isRTL ? "المهام المعلقة" : "Pending Tasks",
                    value: stats.pendingTasks,
                    icon: Clock,
                    color: "bg-yellow-500",
                    bgColor: "bg-yellow-50",
                    textColor: "text-yellow-600"
                  },
                  {
                    title: isRTL ? "نسبة الإنجاز" : "Completion Rate",
                    value: `${stats.overallCompletionPercentage}%`,
                    icon: TrendingUp,
                    color: "bg-purple-500",
                    bgColor: "bg-purple-50",
                    textColor: "text-purple-600"
                  }
                ].map((kpi, index) => (
                  <div key={index} className={`${kpi.bgColor} rounded-xl p-6 border border-gray-200`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 mb-1">{kpi.title}</p>
                        <p className={`text-3xl font-bold ${kpi.textColor}`}>{kpi.value}</p>
                      </div>
                      <div className={`${kpi.color} p-3 rounded-lg`}>
                        <kpi.icon className="w-6 h-6 text-white" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Charts Row 1 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Task Status Distribution - Pie Chart */}
                <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                    <PieChart size={20} className="text-[var(--brand-blue)]" />
                    {isRTL ? "توزيع حالة المهام" : "Task Status Distribution"}
                  </h3>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsPieChart>
                        <Pie
                          data={(() => {
                            const statusCounts = {
                              completed: 0,
                              inProgress: 0,
                              notStarted: 0
                            };

                            systems.forEach(system => {
                              taskTypes.forEach(taskType => {
                                const status = system[taskType.key as keyof System] as SystemTaskStatus;
                                if (status === SystemTaskStatus.DONE || status === SystemTaskStatus.DATA_RECEIVED) {
                                  statusCounts.completed++;
                                } else if (status === SystemTaskStatus.IN_PROGRESS || status === SystemTaskStatus.DATA_REQUESTED || status === SystemTaskStatus.DATA_SENT_AWAITING_REPLY || status === SystemTaskStatus.MEETING_SCHEDULED) {
                                  statusCounts.inProgress++;
                                } else {
                                  statusCounts.notStarted++;
                                }
                              });
                            });

                            return [
                              { name: isRTL ? 'مكتمل' : 'Completed', value: statusCounts.completed, color: '#10B981' },
                              { name: isRTL ? 'قيد التنفيذ' : 'In Progress', value: statusCounts.inProgress, color: '#F59E0B' },
                              { name: isRTL ? 'لم يبدأ' : 'Not Started', value: statusCounts.notStarted, color: '#EF4444' }
                            ];
                          })()}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {(() => {
                            const statusCounts = {
                              completed: 0,
                              inProgress: 0,
                              notStarted: 0
                            };

                            systems.forEach(system => {
                              taskTypes.forEach(taskType => {
                                const status = system[taskType.key as keyof System] as SystemTaskStatus;
                                if (status === SystemTaskStatus.DONE || status === SystemTaskStatus.DATA_RECEIVED) {
                                  statusCounts.completed++;
                                } else if (status === SystemTaskStatus.IN_PROGRESS || status === SystemTaskStatus.DATA_REQUESTED || status === SystemTaskStatus.DATA_SENT_AWAITING_REPLY || status === SystemTaskStatus.MEETING_SCHEDULED) {
                                  statusCounts.inProgress++;
                                } else {
                                  statusCounts.notStarted++;
                                }
                              });
                            });

                            const data = [
                              { name: isRTL ? 'مكتمل' : 'Completed', value: statusCounts.completed, color: '#10B981' },
                              { name: isRTL ? 'قيد التنفيذ' : 'In Progress', value: statusCounts.inProgress, color: '#F59E0B' },
                              { name: isRTL ? 'لم يبدأ' : 'Not Started', value: statusCounts.notStarted, color: '#EF4444' }
                            ];

                            return data.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ));
                          })()}
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  </div>
                </div>

                {/* Groups Performance Chart */}
                <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                    <BarChart3 size={20} className="text-[var(--brand-blue)]" />
                    {isRTL ? "أداء المجموعات" : "Groups Performance"}
                  </h3>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsBarChart data={(() => {
                        const groupedSystems = systems.reduce((acc, system) => {
                          const groupId = system.group;
                          if (groupId) {
                            if (!acc[groupId]) {
                              acc[groupId] = [];
                            }
                            acc[groupId].push(system);
                          }
                          return acc;
                        }, {} as Record<string, System[]>);

                        return Object.entries(groupedSystems)
                          .sort(([a], [b]) => a.localeCompare(b))
                          .map(([groupId, groupSystems]) => {
                            // Calculate systems by status
                            const completedSystems = groupSystems.filter(system => {
                              // A system is completed only when "Final Approval Received?" has "Received Approval" status
                              const finalApprovalStatus = system.finalApprovalStatus as SystemTaskStatus;
                              return finalApprovalStatus === SystemTaskStatus.DONE;
                            }).length;

                            const notStartedSystems = groupSystems.filter(system => {
                              // A system is not started if all tasks are NOT_STARTED
                              return taskTypes.every(taskType => {
                                const status = system[taskType.key as keyof System] as SystemTaskStatus;
                                return !status || status === SystemTaskStatus.NOT_STARTED;
                              });
                            }).length;

                            const inProgressSystems = groupSystems.length - completedSystems - notStartedSystems;

                            return {
                              name: `${isRTL ? 'مجموعة' : 'Group'} ${groupId}`,
                              totalSystems: groupSystems.length,
                              completedSystems: completedSystems,
                              inProgressSystems: inProgressSystems,
                              notStartedSystems: notStartedSystems
                            };
                          });
                      })()}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                          dataKey="name"
                          fontSize={12}
                        />
                        <YAxis />
                        <Tooltip
                          content={({ active, payload, label }) => {
                            if (active && payload && payload.length) {
                              return (
                                <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
                                  <p className="font-semibold text-gray-900 mb-2">{label}</p>
                                  {payload.map((entry, index) => {
                                    const getLabel = (dataKey: string) => {
                                      if (dataKey === 'completedSystems') return isRTL ? 'مكتمل' : 'Completed';
                                      if (dataKey === 'inProgressSystems') return isRTL ? 'قيد التنفيذ' : 'In Progress';
                                      return isRTL ? 'لم يبدأ' : 'Not Started';
                                    };

                                    return (
                                      <div key={index} className="flex items-center gap-2 text-sm">
                                        <div
                                          className="w-3 h-3 rounded-sm"
                                          style={{ backgroundColor: entry.color }}
                                        />
                                        <span className="text-gray-700">
                                          {getLabel(entry.dataKey as string)}: <span className="font-medium">{entry.value}</span>
                                        </span>
                                      </div>
                                    );
                                  })}
                                </div>
                              );
                            }
                            return null;
                          }}
                        />
                        <Legend />
                        <Bar dataKey="completedSystems" stackId="a" fill="#10B981" name={isRTL ? 'مكتمل' : 'Completed'} />
                        <Bar dataKey="inProgressSystems" stackId="a" fill="#F59E0B" name={isRTL ? 'قيد التنفيذ' : 'In Progress'} />
                        <Bar dataKey="notStartedSystems" stackId="a" fill="#EF4444" name={isRTL ? 'لم يبدأ' : 'Not Started'} />
                      </RechartsBarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>

              {/* Charts Row 2 */}
              <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
                {/* Task Status Summary - Donut Chart */}
                <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                    <Target size={20} className="text-[var(--brand-blue)]" />
                    {isRTL ? "ملخص حالة المهام حسب النوع" : "Task Status Summary by Type"}
                  </h3>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsPieChart>
                        <Pie
                          data={(() => {
                            const taskTypeStats = taskTypes.map(taskType => {
                              const completedCount = systems.filter(system => {
                                const status = system[taskType.key as keyof System] as SystemTaskStatus;
                                return status === SystemTaskStatus.DONE || status === SystemTaskStatus.DATA_RECEIVED;
                              }).length;

                              const totalCount = systems.length;
                              const completionRate = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;

                              return {
                                name: taskType.label[lang as 'en' | 'ar'].split(' ').slice(0, 2).join(' '),
                                value: Math.round(completionRate),
                                completed: completedCount,
                                total: totalCount
                              };
                            });

                            return taskTypeStats;
                          })()}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, value }) => `${name}: ${value}%`}
                          outerRadius={100}
                          innerRadius={40}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {taskTypes.map((_, index) => (
                            <Cell key={`cell-${index}`} fill={[
                              '#23A9DB', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'
                            ][index % 5]} />
                          ))}
                        </Pie>
                        <Tooltip
                          formatter={(value, name, props) => [
                            `${value}% (${props.payload.completed}/${props.payload.total})`,
                            isRTL ? 'نسبة الإنجاز' : 'Completion Rate'
                          ]}
                        />
                        <Legend />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>

              {/* Summary Statistics Table */}
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <FileText size={20} className="text-[var(--brand-blue)]" />
                  {isRTL ? "إحصائيات مفصلة" : "Detailed Statistics"}
                </h3>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className={`py-3 px-4 font-semibold text-gray-900 ${isRTL ? 'text-right' : 'text-left'}`}>
                          {isRTL ? 'نوع المهمة' : 'Task Type'}
                        </th>
                        <th className={`py-3 px-4 font-semibold text-gray-900 ${isRTL ? 'text-right' : 'text-left'}`}>
                          {isRTL ? 'مكتمل' : 'Completed'}
                        </th>
                        <th className={`py-3 px-4 font-semibold text-gray-900 ${isRTL ? 'text-right' : 'text-left'}`}>
                          {isRTL ? 'الإجمالي' : 'Total'}
                        </th>
                        <th className={`py-3 px-4 font-semibold text-gray-900 ${isRTL ? 'text-right' : 'text-left'}`}>
                          {isRTL ? 'النسبة المئوية' : 'Percentage'}
                        </th>
                        <th className={`py-3 px-4 font-semibold text-gray-900 ${isRTL ? 'text-right' : 'text-left'}`}>
                          {isRTL ? 'الحالة' : 'Status'}
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {taskTypes.map((taskType, index) => {
                        const completedCount = systems.filter(system => {
                          const status = system[taskType.key as keyof System] as SystemTaskStatus;
                          return status === SystemTaskStatus.DONE || status === SystemTaskStatus.DATA_RECEIVED;
                        }).length;

                        const totalCount = systems.length;
                        const completionRate = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;

                        return (
                          <tr key={taskType.key} className={`border-b border-gray-100 ${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}`}>
                            <td className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                              {taskType.label[lang as 'en' | 'ar']}
                            </td>
                            <td className={`py-3 px-4 font-semibold text-green-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                              {completedCount}
                            </td>
                            <td className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                              {totalCount}
                            </td>
                            <td className={`py-3 px-4 font-semibold ${isRTL ? 'text-right' : 'text-left'}`}>
                              <span className={`${
                                completionRate >= 80 ? 'text-green-600' :
                                completionRate >= 50 ? 'text-yellow-600' :
                                'text-red-600'
                              }`}>
                                {Math.round(completionRate)}%
                              </span>
                            </td>
                            <td className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                completionRate >= 80 ? 'bg-green-100 text-green-800' :
                                completionRate >= 50 ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                                {completionRate >= 80 ? (isRTL ? 'ممتاز' : 'Excellent') :
                                 completionRate >= 50 ? (isRTL ? 'جيد' : 'Good') :
                                 (isRTL ? 'يحتاج تحسين' : 'Needs Improvement')}
                              </span>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* System Task Status Tab */}
          <TabsContent value="status">
            {/* Group Summary - Professional Business Design */}
      <div className="px-6 py-4">
        <div className="bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-[var(--brand-blue)] to-blue-600 rounded-lg flex items-center justify-center shadow-md">
                <BarChart3 className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900">
                  {isRTL ? "لوحة تحكم المجموعات" : "Group Performance Dashboard"}
                </h3>
                <p className="text-sm text-gray-600">
                  {isRTL ? "تتبع تقدم المهام حسب المجموعة" : "Track task progress by group"}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <TrendingUp className="w-4 h-4" />
              {isRTL ? "التقدم الإجمالي" : "Overall Progress"}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
            {(() => {
              const groupedSystems = groupSystemsByGroup();
              const groupItems = [];

              // Groups 1-9
              for (let i = 1; i <= 9; i++) {
                const groupSystems = groupedSystems[i.toString()];
                const count = groupSystems.length;
                const progress = calculateGroupProgress(groupSystems);

                if (count > 0) {
                  groupItems.push(
                    <div
                      key={i}
                      className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105 hover:border-[var(--brand-blue)]"
                      onClick={() => handleGroupDetailClick(i.toString(), `${isRTL ? 'المجموعة' : 'Group'} ${i}`, groupSystems, progress)}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <div className="w-8 h-8 bg-gradient-to-br from-[var(--brand-blue)] to-blue-600 rounded-lg flex items-center justify-center text-white text-sm font-bold">
                            {i}
                          </div>
                          <div>
                            <div className="text-sm font-semibold text-gray-900">
                              {isRTL ? `المجموعة ${i}` : `Group ${i}`}
                            </div>
                            <div className="text-xs text-gray-500">
                              {count} {isRTL ? "نظام" : count === 1 ? "system" : "systems"}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-[var(--brand-blue)]">
                            {progress}%
                          </div>
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                        <div
                          className="bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>

                      <div className="text-xs text-gray-600 text-center">
                        {isRTL ? "انقر للتفاصيل" : "Click for Details"}
                      </div>
                    </div>
                  );
                }
              }

              // Unassigned
              const unassignedSystems = groupedSystems['unassigned'];
              const unassignedCount = unassignedSystems.length;
              if (unassignedCount > 0) {
                const unassignedProgress = calculateGroupProgress(unassignedSystems);
                groupItems.push(
                  <div
                    key="unassigned"
                    className="bg-white rounded-lg border border-gray-300 p-4 hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-105 hover:border-gray-400"
                    onClick={() => handleGroupDetailClick('unassigned', isRTL ? 'بدون مجموعة' : 'Unassigned', unassignedSystems, unassignedProgress)}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-500 rounded-lg flex items-center justify-center text-white">
                          <Database className="w-4 h-4" />
                        </div>
                        <div>
                          <div className="text-sm font-semibold text-gray-700">
                            {isRTL ? "بدون مجموعة" : "Unassigned"}
                          </div>
                          <div className="text-xs text-gray-500">
                            {unassignedCount} {isRTL ? "نظام" : unassignedCount === 1 ? "system" : "systems"}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-gray-600">
                          {unassignedProgress}%
                        </div>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                      <div
                        className="bg-gradient-to-r from-gray-400 to-gray-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${unassignedProgress}%` }}
                      ></div>
                    </div>

                    <div className="text-xs text-gray-600 text-center">
                      {isRTL ? "انقر للتفاصيل" : "Click for Details"}
                    </div>
                  </div>
                );
              }

              return groupItems;
            })()}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-6 py-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Table Header */}
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-[var(--brand-blue)] rounded-lg flex items-center justify-center">
                  <Database className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">
                    {isRTL ? "إدارة مهام الأنظمة المؤسسية" : "Enterprise System Task Management"}
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">
                    {isRTL ? "تتبع شامل لتقدم المهام والموافقات النهائية" : "Comprehensive tracking of task progress and final approvals"}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-6">
                {/* Export Button */}
                <Button
                  onClick={exportToExcel}
                  disabled={isExporting}
                  className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
                >
                  {isExporting ? (
                    <Loader className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Download className="w-4 h-4 mr-2" />
                  )}
                  {isRTL ? "تصدير التقرير التنفيذي" : "Export Executive Report"}
                </Button>

                {/* Status Legend */}
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="w-4 h-4 text-green-600" />
                    <span>{isRTL ? "مكتمل" : "Completed"}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Loader className="w-4 h-4 text-blue-600" />
                    <span>{isRTL ? "قيد التنفيذ" : "In Progress"}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Circle className="w-4 h-4 text-gray-400" />
                    <span>{isRTL ? "لم يبدأ" : "Not Started"}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-72">
                    <div className="flex items-center gap-2">
                      <Database className="w-4 h-4" />
                      {isRTL ? "النظام" : "System"}
                    </div>
                  </th>
                  {taskTypes.map((taskType) => (
                    <th key={taskType.key} className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-48">
                      <div className="min-w-[180px] text-center">
                        {taskType.label[lang as 'en' | 'ar']}
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {isLoading ? (
                  <tr>
                    <td colSpan={taskTypes.length + 1} className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center gap-3">
                        <Loader className="w-6 h-6 text-blue-600 animate-spin" />
                        <span className="text-gray-500">{isRTL ? "جاري التحميل..." : "Loading..."}</span>
                      </div>
                    </td>
                  </tr>
                ) : systems.length === 0 ? (
                  <tr>
                    <td colSpan={taskTypes.length + 1} className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center gap-3">
                        <Database className="w-8 h-8 text-gray-300" />
                        <span className="text-gray-500">{isRTL ? "لا توجد أنظمة" : "No systems found"}</span>
                      </div>
                    </td>
                  </tr>
                ) : (
                  (() => {
                    const groupedSystems = groupSystemsByGroup();
                    const allRows: React.ReactElement[] = [];

                    // Render groups 1-9 in order
                    for (let i = 1; i <= 9; i++) {
                      const groupSystems = groupedSystems[i.toString()];
                      if (groupSystems.length > 0) {
                        const groupId = `group-${i}`;
                        const isMinimized = minimizedGroups.has(groupId);
                        const groupProgress = calculateGroupProgress(groupSystems);

                        // Add group header row
                        allRows.push(
                          <tr key={groupId} className="bg-gradient-to-r from-slate-50 to-blue-50 border-l-4 border-l-[var(--brand-blue)] hover:from-slate-100 hover:to-blue-100 transition-all duration-200">
                            <td colSpan={taskTypes.length + 1} className="px-4 py-4">
                              <div className="flex items-center justify-between">
                                <div
                                  className="flex items-center gap-4 cursor-pointer flex-1"
                                  onClick={() => toggleGroupMinimization(groupId)}
                                >
                                  <div className="flex items-center gap-2">
                                    <button className="p-1 hover:bg-white/50 rounded-md transition-colors">
                                      {isMinimized ? (
                                        <ChevronRight className="w-4 h-4 text-[var(--brand-blue)]" />
                                      ) : (
                                        <ChevronDown className="w-4 h-4 text-[var(--brand-blue)]" />
                                      )}
                                    </button>
                                    <div className="w-10 h-10 bg-gradient-to-br from-[var(--brand-blue)] to-blue-600 rounded-lg flex items-center justify-center shadow-md">
                                      <span className="text-white font-bold text-sm">{i}</span>
                                    </div>
                                  </div>
                                  <div className="flex-1">
                                    <div className="flex items-center gap-3 mb-1">
                                      <h3 className="text-base font-bold text-gray-900">
                                        {isRTL ? `المجموعة ${i}` : `Group ${i}`}
                                      </h3>
                                      <div className="px-2 py-1 bg-[var(--brand-blue)]/10 rounded-full">
                                        <span className="text-xs font-semibold text-[var(--brand-blue)]">
                                          {groupSystems.length} {isRTL ? "نظام" : groupSystems.length === 1 ? "system" : "systems"}
                                        </span>
                                      </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                      <div className="flex-1 max-w-xs">
                                        <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                                          <span>{isRTL ? "التقدم" : "Progress"}</span>
                                          <span className="font-semibold">{groupProgress}%</span>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-2">
                                          <div
                                            className="bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 h-2 rounded-full transition-all duration-500"
                                            style={{ width: `${groupProgress}%` }}
                                          ></div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-2 text-xs text-gray-500">
                                    <span>{isMinimized ? (isRTL ? "توسيع" : "Expand") : (isRTL ? "تصغير" : "Minimize")}</span>
                                  </div>
                                </div>
                              </div>
                            </td>
                          </tr>
                        );

                        // Add bulk update buttons row
                        if (!isMinimized) {
                          allRows.push(
                            <tr key={`${groupId}-bulk`} className="bg-blue-50/50 border-l-4 border-l-blue-300">
                              <td className="px-4 py-2 text-xs font-medium text-gray-600 w-72">
                                <div className="flex items-center gap-2">
                                  <Users className="w-3 h-3 text-blue-600" />
                                  {isRTL ? "تحديث مجمع للمجموعة" : "Bulk Update Group"}
                                </div>
                              </td>
                              {taskTypes.map((taskType) => (
                                <td key={taskType.key} className="px-3 py-2 text-center">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setBulkUpdateModal({
                                        groupNumber: i.toString(),
                                        taskType: taskType.key,
                                        groupSystems: groupSystems
                                      });
                                      // Set the first valid status for this task type
                                      const firstValidStatus = taskType.statuses[0]?.value || SystemTaskStatus.NOT_STARTED;
                                      setBulkStatus(firstValidStatus);
                                      setBulkNote('');
                                      setBulkLink('');
                                      setBulkStartDate('');
                                      setBulkEndDate('');
                                    }}
                                    className="h-7 px-2 text-xs bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                                    title={isRTL ? `تحديث جميع الأنظمة في المجموعة ${i}` : `Update all systems in Group ${i}`}
                                  >
                                    <Users className="w-3 h-3 mr-1" />
                                    {isRTL ? "تحديث" : "Update"}
                                  </Button>
                                </td>
                              ))}
                            </tr>
                          );
                        }

                        // Add systems in this group (only if not minimized)
                        if (!isMinimized) {
                          groupSystems.forEach((system) => {
                            const isCompleted = SystemsService.isSystemCompleted(system);
                            allRows.push(
                            <tr
                              key={system.id}
                              className={`hover:bg-gray-50 ${
                                isCompleted
                                  ? 'bg-green-50 border-l-4 border-l-green-400'
                                  : 'border-l-4 border-l-transparent'
                              }`}
                            >
                              <td className="px-4 py-3 align-top">
                                <div className="flex items-start gap-2 w-72">
                                  <div className="flex-shrink-0 mt-1">
                                    {isCompleted ? (
                                      <CheckCircle2 className="w-4 h-4 text-green-600" />
                                    ) : (
                                      <Circle className="w-4 h-4 text-gray-400" />
                                    )}
                                  </div>
                                  <div className="min-w-0 flex-1">
                                    <div className="text-sm font-medium text-gray-900 mb-1">{system.name}</div>
                                    <div className="text-xs text-gray-500">{system.responsibleOwner}</div>
                                    {system.consultantName && (
                                      <div className="text-xs text-green-600 font-medium mt-1">
                                        {isRTL ? `المستشار: ${system.consultantName}` : `Consultant: ${system.consultantName}`}
                                      </div>
                                    )}
                                    {system.group && (
                                      <div className="text-xs text-[var(--brand-blue)] font-medium mt-1">
                                        {isRTL ? `المجموعة ${system.group}` : `Group ${system.group}`}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </td>
                        {taskTypes.map((taskType) => {
                          const status = system[taskType.key as keyof System] as SystemTaskStatus || SystemTaskStatus.NOT_STARTED;
                          const startDate = system[`${taskType.key.replace('Status', 'StartDate')}` as keyof System] as Timestamp;
                          const endDate = system[`${taskType.key.replace('Status', 'EndDate')}` as keyof System] as Timestamp;
                          const note = system[`${taskType.key.replace('Status', 'Note')}` as keyof System] as string;
                          const meetingId = taskType.key === 'classificationReviewStatus' ? system.classificationReviewMeetingId : undefined;
                          const hasNote = note && note.trim().length > 0;
                          const hasMeeting = meetingId && meetingId.trim().length > 0;

                          return (
                            <td key={taskType.key} className="px-3 py-3 align-top">
                              <div className="min-w-[180px] space-y-2">
                                {/* Enhanced Status Display */}
                                <div className="space-y-1">
                                  <Select
                                    value={status}
                                    onValueChange={(newStatus) => handleStatusChange(system.id!, taskType.key, newStatus as SystemTaskStatus)}
                                  >
                                    <SelectTrigger className={`w-full text-xs h-auto py-2 px-3 ${getStatusColor(status)} hover:shadow-sm transition-all duration-200 border-2`}>
                                      <div className="flex items-center gap-2 w-full">
                                        <div className="flex items-center gap-2 flex-1">
                                          {getStatusIcon(status)}
                                          <span className="font-medium truncate">
                                            {taskType.statuses.find(s => s.value === status)?.label[lang as 'en' | 'ar'] || 'Unknown'}
                                          </span>
                                        </div>
                                        <ChevronDown className="w-3 h-3 opacity-50" />
                                      </div>
                                    </SelectTrigger>
                                    <SelectContent className="min-w-[200px]">
                                      {taskType.statuses.map((statusOption) => (
                                        <SelectItem key={statusOption.value} value={statusOption.value} className="py-2">
                                          <div className="flex items-center gap-3">
                                            <div className="flex items-center gap-2">
                                              {getStatusIcon(statusOption.value)}
                                              <span className="font-medium">{statusOption.label[lang as 'en' | 'ar']}</span>
                                            </div>
                                          </div>
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>

                                {/* Dates - More Compact */}
                                {(startDate || endDate) && (
                                  <div className="text-xs text-gray-600 space-y-1">
                                    {startDate && (
                                      <div className="flex items-center gap-1">
                                        <div className="w-1.5 h-1.5 rounded-full bg-green-500"></div>
                                        <span>{formatDate(startDate)}</span>
                                      </div>
                                    )}
                                    {endDate && (
                                      <div className="flex items-center gap-1">
                                        <div className="w-1.5 h-1.5 rounded-full bg-red-500"></div>
                                        <span>{formatDate(endDate)}</span>
                                      </div>
                                    )}
                                  </div>
                                )}

                                {/* Enhanced Action Panel */}
                                <div className="bg-white border border-gray-200 rounded-lg p-2">
                                  <div className="text-xs font-medium text-gray-700 mb-2 flex items-center gap-1">
                                    <Activity className="w-3 h-3" />
                                    {isRTL ? "الإجراءات" : "Actions"}
                                  </div>
                                  <div className="flex items-center gap-1">
                                    {/* Notes Button */}
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => {
                                        setNotesModal({
                                          systemId: system.id!,
                                          taskType: taskType.key,
                                          currentNote: note || ''
                                        });
                                        setTempNote(note || '');
                                      }}
                                      className={`h-7 w-7 p-0 rounded-md ${
                                        hasNote
                                          ? 'bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100'
                                          : 'hover:bg-gray-50 border-gray-200'
                                      }`}
                                      title={hasNote ? (isRTL ? "يحتوي على ملاحظة" : "Has note") : (isRTL ? "إضافة ملاحظة" : "Add note")}
                                    >
                                      <FileText className={`w-3 h-3 ${hasNote ? 'text-orange-600' : 'text-gray-400'}`} />
                                    </Button>

                                  {/* Link Button for tasks with hasLink property */}
                                  {taskType.hasLink && taskType.key !== 'classificationReviewStatus' && (
                                    (() => {
                                      const linkFieldName = `${taskType.key.replace('Status', 'Link')}` as keyof System;
                                      const hasLink = system[linkFieldName] as string;
                                      return (
                                        <Button
                                          size="sm"
                                          variant="outline"
                                          onClick={() => {
                                            setLinkModal({
                                              systemId: system.id!,
                                              taskType: taskType.key,
                                              currentLink: hasLink || ''
                                            });
                                            setTempLink(hasLink || '');
                                          }}
                                          className={`h-6 w-6 p-0 ${
                                            hasLink
                                              ? 'bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100'
                                              : 'hover:bg-blue-50 hover:border-blue-200'
                                          }`}
                                          title={hasLink ? (isRTL ? "عرض/تعديل الرابط" : "View/Edit link") : (isRTL ? "إرفاق رابط" : "Attach link")}
                                        >
                                          <Link className={`w-3 h-3 ${hasLink ? 'text-blue-600' : 'text-gray-400'}`} />
                                        </Button>
                                      );
                                    })()
                                  )}

                                  {/* Meeting Button for Classification Review */}
                                  {taskType.key === 'classificationReviewStatus' && (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => {
                                        if (hasMeeting) {
                                          // Show meeting details if already linked
                                          const meeting = meetings.find(m => m.id === meetingId);
                                          if (meeting) setMeetingDetailsModal({meeting, systemId: system.id!});
                                        } else {
                                          // Show meeting selection modal if not linked
                                          setMeetingModal({
                                            systemId: system.id!,
                                            currentMeetingId: meetingId
                                          });
                                        }
                                      }}
                                      className={`h-6 w-6 p-0 ${
                                        hasMeeting
                                          ? 'bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100'
                                          : 'hover:bg-gray-50'
                                      }`}
                                      title={hasMeeting ? (isRTL ? "عرض تفاصيل الاجتماع" : "View meeting details") : (isRTL ? "ربط اجتماع" : "Link meeting")}
                                    >
                                      {hasMeeting ? (
                                        <Eye className="w-3 h-3 text-blue-600" />
                                      ) : (
                                        <Users className="w-3 h-3 text-gray-400" />
                                      )}
                                    </Button>
                                  )}

                                  {/* Date Edit Button */}
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      setDateEditModal({
                                        systemId: system.id!,
                                        taskType: taskType.key,
                                        currentStartDate: startDate,
                                        currentEndDate: endDate
                                      });

                                      // Format dates based on task type
                                      if (taskType.key === 'classificationDataStatus' || taskType.key === 'finalApprovalStatus') {
                                        // Single date - use date format
                                        setTempStartDate(startDate ? startDate.toDate().toISOString().slice(0, 10) : '');
                                        setTempEndDate('');
                                      } else {
                                        // Start and end dates - use datetime format
                                        setTempStartDate(startDate ? startDate.toDate().toISOString().slice(0, 16) : '');
                                        setTempEndDate(endDate ? endDate.toDate().toISOString().slice(0, 16) : '');
                                      }
                                    }}
                                    className={`h-7 w-7 p-0 rounded-md ${
                                      (startDate || endDate)
                                        ? 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100'
                                        : 'hover:bg-gray-50 border-gray-200'
                                    }`}
                                    title={isRTL ? "تعديل التواريخ" : "Edit dates"}
                                  >
                                    <CalendarDays className={`w-3 h-3 ${(startDate || endDate) ? 'text-green-600' : 'text-gray-400'}`} />
                                  </Button>

                                  {/* Approval Delay Reason Button - Only for Final Approval in Actions */}
                                  {taskType.key === 'finalApprovalStatus' && (status === SystemTaskStatus.APPROVAL_DELAYED || status === SystemTaskStatus.IN_PROGRESS || status === SystemTaskStatus.NOT_STARTED) && (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => {
                                        setApprovalDelayModal({
                                          systemId: system.id!,
                                          currentReason: note || ''
                                        });
                                        setTempDelayReason(note || '');
                                      }}
                                      className={`h-7 w-7 p-0 rounded-md ${
                                        status === SystemTaskStatus.APPROVAL_DELAYED && note && note.trim().length > 0
                                          ? 'bg-red-100 border-red-300 text-red-800 hover:bg-red-200'
                                          : 'bg-red-50 border-red-200 text-red-700 hover:bg-red-100'
                                      }`}
                                      title={
                                        status === SystemTaskStatus.APPROVAL_DELAYED
                                          ? (isRTL ? "تحديث سبب التأخير" : "Update delay reason")
                                          : (isRTL ? "إضافة سبب تأخير الموافقة" : "Add approval delay reason")
                                      }
                                    >
                                      <AlertTriangle className="w-3 h-3 text-red-600" />
                                    </Button>
                                  )}

                                  {/* Delay History Button - Only for Final Approval when DONE and has delay history in Actions */}
                                  {taskType.key === 'finalApprovalStatus' && status === SystemTaskStatus.DONE && system.finalApprovalDelayHistory && system.finalApprovalDelayHistory.length > 0 && (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => {
                                        setDelayHistoryModal({
                                          systemId: system.id!,
                                          systemName: system.name,
                                          delayHistory: system.finalApprovalDelayHistory || []
                                        });
                                      }}
                                      className="h-7 w-7 p-0 rounded-md bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100"
                                      title={isRTL ? "تاريخ التأخيرات" : "Delay history"}
                                    >
                                      <Clock className="w-3 h-3 text-orange-600" />
                                    </Button>
                                  )}

                                </div>
                              </div>
                            </div>
                            </td>
                            );
                          })}
                            </tr>
                            );
                          });
                        }
                      }
                    }

                    // Add unassigned systems at the end if any exist
                    const unassignedSystems = groupedSystems['unassigned'];
                    if (unassignedSystems.length > 0) {
                      const unassignedGroupId = 'group-unassigned';
                      const isUnassignedMinimized = minimizedGroups.has(unassignedGroupId);
                      const unassignedProgress = calculateGroupProgress(unassignedSystems);

                      // Add unassigned group header
                      allRows.push(
                        <tr key={unassignedGroupId} className="bg-gradient-to-r from-gray-50 to-gray-100 border-l-4 border-l-gray-400 hover:from-gray-100 hover:to-gray-200 transition-all duration-200">
                          <td colSpan={taskTypes.length + 1} className="px-4 py-4">
                            <div
                              className="flex items-center justify-between cursor-pointer"
                              onClick={() => toggleGroupMinimization(unassignedGroupId)}
                            >
                              <div className="flex items-center gap-4">
                                <div className="flex items-center gap-2">
                                  <button className="p-1 hover:bg-white/50 rounded-md transition-colors">
                                    {isUnassignedMinimized ? (
                                      <ChevronRight className="w-4 h-4 text-gray-600" />
                                    ) : (
                                      <ChevronDown className="w-4 h-4 text-gray-600" />
                                    )}
                                  </button>
                                  <div className="w-10 h-10 bg-gradient-to-br from-gray-400 to-gray-500 rounded-lg flex items-center justify-center shadow-md">
                                    <Database className="w-5 h-5 text-white" />
                                  </div>
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center gap-3 mb-1">
                                    <h3 className="text-base font-bold text-gray-700">
                                      {isRTL ? "بدون مجموعة" : "Unassigned"}
                                    </h3>
                                    <div className="px-2 py-1 bg-gray-400/10 rounded-full">
                                      <span className="text-xs font-semibold text-gray-600">
                                        {unassignedSystems.length} {isRTL ? "نظام" : unassignedSystems.length === 1 ? "system" : "systems"}
                                      </span>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-3">
                                    <div className="flex-1 max-w-xs">
                                      <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                                        <span>{isRTL ? "التقدم" : "Progress"}</span>
                                        <span className="font-semibold">{unassignedProgress}%</span>
                                      </div>
                                      <div className="w-full bg-gray-200 rounded-full h-2">
                                        <div
                                          className="bg-gradient-to-r from-gray-400 to-gray-500 h-2 rounded-full transition-all duration-500"
                                          style={{ width: `${unassignedProgress}%` }}
                                        ></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center gap-2 text-xs text-gray-500">
                                <span>{isUnassignedMinimized ? (isRTL ? "توسيع" : "Expand") : (isRTL ? "تصغير" : "Minimize")}</span>
                              </div>
                            </div>
                          </td>
                        </tr>
                      );

                      // Add unassigned systems (only if not minimized)
                      if (!isUnassignedMinimized) {
                        unassignedSystems.forEach((system) => {
                        const isCompleted = SystemsService.isSystemCompleted(system);
                        allRows.push(
                          <tr
                            key={system.id}
                            className={`hover:bg-gray-50 ${
                              isCompleted
                                ? 'bg-green-50 border-l-4 border-l-green-400'
                                : 'border-l-4 border-l-transparent'
                            }`}
                          >
                            <td className="px-4 py-3 align-top">
                              <div className="flex items-start gap-2 w-72">
                                <div className="flex-shrink-0 mt-1">
                                  {isCompleted ? (
                                    <CheckCircle2 className="w-4 h-4 text-green-600" />
                                  ) : (
                                    <Circle className="w-4 h-4 text-gray-400" />
                                  )}
                                </div>
                                <div className="min-w-0 flex-1">
                                  <div className="text-sm font-medium text-gray-900 mb-1">{system.name}</div>
                                  <div className="text-xs text-gray-500">{system.responsibleOwner}</div>
                                  {system.consultantName && (
                                    <div className="text-xs text-green-600 font-medium mt-1">
                                      {isRTL ? `المستشار: ${system.consultantName}` : `Consultant: ${system.consultantName}`}
                                    </div>
                                  )}
                                  <div className="text-xs text-gray-400 font-medium mt-1">
                                    {isRTL ? "بدون مجموعة" : "No group assigned"}
                                  </div>
                                </div>
                              </div>
                            </td>
                            {taskTypes.map((taskType) => {
                              const status = system[taskType.key as keyof System] as SystemTaskStatus || SystemTaskStatus.NOT_STARTED;
                              const startDate = system[`${taskType.key.replace('Status', 'StartDate')}` as keyof System] as Timestamp;
                              const endDate = system[`${taskType.key.replace('Status', 'EndDate')}` as keyof System] as Timestamp;
                              const note = system[`${taskType.key.replace('Status', 'Note')}` as keyof System] as string;
                              const meetingId = taskType.key === 'classificationReviewStatus' ? system.classificationReviewMeetingId : undefined;
                              const hasNote = note && note.trim().length > 0;
                              const hasMeeting = meetingId && meetingId.trim().length > 0;

                              return (
                                <td key={taskType.key} className="px-3 py-3 align-top">
                                  <div className="min-w-[180px] space-y-2">
                                    {/* Enhanced Status Display */}
                                    <div className="space-y-1">
                                      <Select
                                        value={status}
                                        onValueChange={(newStatus) => handleStatusChange(system.id!, taskType.key, newStatus as SystemTaskStatus)}
                                      >
                                        <SelectTrigger className={`w-full text-xs h-auto py-2 px-3 ${getStatusColor(status)} hover:shadow-sm transition-all duration-200 border-2`}>
                                          <div className="flex items-center gap-2 w-full">
                                            <div className="flex items-center gap-2 flex-1">
                                              {getStatusIcon(status)}
                                              <span className="font-medium truncate">
                                                {taskType.statuses.find(s => s.value === status)?.label[lang as 'en' | 'ar'] || 'Unknown'}
                                              </span>
                                            </div>
                                            <ChevronDown className="w-3 h-3 opacity-50" />
                                          </div>
                                        </SelectTrigger>
                                        <SelectContent className="min-w-[200px]">
                                          {taskType.statuses.map((statusOption) => (
                                            <SelectItem key={statusOption.value} value={statusOption.value} className="py-2">
                                              <div className="flex items-center gap-3">
                                                <div className="flex items-center gap-2">
                                                  {getStatusIcon(statusOption.value)}
                                                  <span className="font-medium">{statusOption.label[lang as 'en' | 'ar']}</span>
                                                </div>
                                              </div>
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>

                                    {/* Dates - More Compact */}
                                    {(startDate || endDate) && (
                                      <div className="text-xs text-gray-600 space-y-1">
                                        {startDate && (
                                          <div className="flex items-center gap-1">
                                            <div className="w-1.5 h-1.5 rounded-full bg-green-500"></div>
                                            <span>{formatDate(startDate)}</span>
                                          </div>
                                        )}
                                        {endDate && (
                                          <div className="flex items-center gap-1">
                                            <div className="w-1.5 h-1.5 rounded-full bg-red-500"></div>
                                            <span>{formatDate(endDate)}</span>
                                          </div>
                                        )}
                                      </div>
                                    )}

                                    {/* Action Buttons */}
                                    <div className="flex items-center gap-1">
                                      {/* Notes Button */}
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => {
                                          setNotesModal({
                                            systemId: system.id!,
                                            taskType: taskType.key,
                                            currentNote: note || ''
                                          });
                                          setTempNote(note || '');
                                        }}
                                        className={`h-6 w-6 p-0 ${
                                          hasNote
                                            ? 'bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100'
                                            : 'hover:bg-gray-50'
                                        }`}
                                        title={hasNote ? (isRTL ? "يحتوي على ملاحظة" : "Has note") : (isRTL ? "إضافة ملاحظة" : "Add note")}
                                      >
                                        <FileText className={`w-3 h-3 ${hasNote ? 'text-orange-600' : 'text-gray-400'}`} />
                                      </Button>

                                      {/* Meeting Button for Classification Review */}
                                      {taskType.key === 'classificationReviewStatus' && (
                                        <Button
                                          size="sm"
                                          variant="outline"
                                          onClick={() => {
                                            if (hasMeeting) {
                                              // Show meeting details if already linked
                                              const meeting = meetings.find(m => m.id === meetingId);
                                              if (meeting) setMeetingDetailsModal({meeting, systemId: system.id!});
                                            } else {
                                              // Show meeting selection modal if not linked
                                              setMeetingModal({
                                                systemId: system.id!,
                                                currentMeetingId: meetingId
                                              });
                                            }
                                          }}
                                          className={`h-6 w-6 p-0 ${
                                            hasMeeting
                                              ? 'bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100'
                                              : 'hover:bg-gray-50'
                                          }`}
                                          title={hasMeeting ? (isRTL ? "عرض تفاصيل الاجتماع" : "View meeting details") : (isRTL ? "ربط اجتماع" : "Link meeting")}
                                        >
                                          {hasMeeting ? (
                                            <Eye className="w-3 h-3 text-blue-600" />
                                          ) : (
                                            <Users className="w-3 h-3 text-gray-400" />
                                          )}
                                        </Button>
                                      )}

                                      {/* Date Edit Button */}
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => {
                                          setDateEditModal({
                                            systemId: system.id!,
                                            taskType: taskType.key,
                                            currentStartDate: startDate,
                                            currentEndDate: endDate
                                          });

                                          // Format dates based on task type
                                          if (taskType.key === 'classificationDataStatus' || taskType.key === 'finalApprovalStatus') {
                                            // Single date - use date format
                                            setTempStartDate(startDate ? startDate.toDate().toISOString().slice(0, 10) : '');
                                            setTempEndDate('');
                                          } else {
                                            // Start and end dates - use datetime format
                                            setTempStartDate(startDate ? startDate.toDate().toISOString().slice(0, 16) : '');
                                            setTempEndDate(endDate ? endDate.toDate().toISOString().slice(0, 16) : '');
                                          }
                                        }}
                                        className={`h-6 w-6 p-0 ${
                                          (startDate || endDate)
                                            ? 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100'
                                            : 'hover:bg-gray-50'
                                        }`}
                                        title={isRTL ? "تعديل التواريخ" : "Edit dates"}
                                      >
                                        <CalendarDays className={`w-3 h-3 ${(startDate || endDate) ? 'text-green-600' : 'text-gray-400'}`} />
                                      </Button>

                                      {/* Approval Delay Reason Button - Only for Final Approval */}
                                      {taskType.key === 'finalApprovalStatus' && (status === SystemTaskStatus.APPROVAL_DELAYED || status === SystemTaskStatus.IN_PROGRESS || status === SystemTaskStatus.NOT_STARTED) && (
                                        <Button
                                          size="sm"
                                          variant="outline"
                                          onClick={() => {
                                            setApprovalDelayModal({
                                              systemId: system.id!,
                                              currentReason: note || ''
                                            });
                                            setTempDelayReason(note || '');
                                          }}
                                          className={`h-6 w-6 p-0 ${
                                            status === SystemTaskStatus.APPROVAL_DELAYED && note && note.trim().length > 0
                                              ? 'bg-red-100 border-red-300 text-red-800 hover:bg-red-200'
                                              : 'bg-red-50 border-red-200 text-red-700 hover:bg-red-100'
                                          }`}
                                          title={
                                            status === SystemTaskStatus.APPROVAL_DELAYED
                                              ? (isRTL ? "تحديث سبب التأخير" : "Update delay reason")
                                              : (isRTL ? "إضافة سبب تأخير الموافقة" : "Add approval delay reason")
                                          }
                                        >
                                          <AlertTriangle className="w-3 h-3 text-red-600" />
                                        </Button>
                                      )}

                                      {/* Delay History Button - Only for Final Approval when DONE and has delay history */}
                                      {taskType.key === 'finalApprovalStatus' && status === SystemTaskStatus.DONE && system.finalApprovalDelayHistory && system.finalApprovalDelayHistory.length > 0 && (
                                        <Button
                                          size="sm"
                                          variant="outline"
                                          onClick={() => {
                                            setDelayHistoryModal({
                                              systemId: system.id!,
                                              systemName: system.name,
                                              delayHistory: system.finalApprovalDelayHistory || []
                                            });
                                          }}
                                          className="h-6 w-6 p-0 bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100"
                                          title={isRTL ? "تاريخ التأخيرات" : "Delay history"}
                                        >
                                          <Clock className="w-3 h-3 text-orange-600" />
                                        </Button>
                                      )}
                                    </div>
                                  </div>
                                </td>
                              );
                            })}
                          </tr>
                          );
                        });
                      }
                    }

                    return allRows;
                  })()
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Notes Modal */}
      <Dialog open={!!notesModal} onOpenChange={() => setNotesModal(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="pb-4">
            <DialogTitle className="text-lg font-semibold flex items-center gap-2">
              <FileText className="w-5 h-5 text-orange-600" />
              {isRTL ? "إدارة الملاحظة" : "Task Note"}
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-1">
              {isRTL ? "أضف أو عدل الملاحظة للمهمة" : "Add or edit note for this task"}
            </p>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                {isRTL ? "الملاحظة (حد أقصى 20 كلمة)" : "Note (max 20 words)"}
              </label>
              <Textarea
                value={tempNote}
                onChange={(e) => setTempNote(e.target.value)}
                placeholder={isRTL ? "أدخل ملاحظة مفيدة..." : "Enter a helpful note..."}
                rows={4}
                className="resize-none border-gray-200 focus:border-amber-400 focus:ring-amber-400"
              />
              <div className="text-xs text-gray-500 text-right">
                {tempNote.split(' ').filter(word => word.length > 0).length}/20 {isRTL ? "كلمة" : "words"}
              </div>
            </div>
            <div className="flex justify-end gap-3 pt-2">
              <Button variant="outline" onClick={() => setNotesModal(null)} className="px-4">
                <X className="w-4 h-4 mr-2" />
                {isRTL ? "إلغاء" : "Cancel"}
              </Button>
              <Button onClick={handleSaveNote} className="px-4 bg-amber-600 hover:bg-amber-700">
                <Save className="w-4 h-4 mr-2" />
                {isRTL ? "حفظ الملاحظة" : "Save Note"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Meeting Modal */}
      <Dialog open={!!meetingModal} onOpenChange={() => setMeetingModal(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Users className="w-5 h-5 text-blue-600" />
              {isRTL ? "ربط اجتماع" : "Link Meeting"}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                {isRTL ? "اختر اجتماع" : "Select a meeting"}
              </label>
              <Select
                value={meetingModal?.currentMeetingId || ''}
                onValueChange={handleLinkMeeting}
              >
                <SelectTrigger>
                  <SelectValue placeholder={isRTL ? "اختر اجتماع" : "Select a meeting"} />
                </SelectTrigger>
                <SelectContent>
                  {meetings.map((meeting) => (
                    <SelectItem key={meeting.id} value={meeting.id!}>
                      <div className="flex flex-col">
                        <span className="font-medium">{meeting.title}</span>
                        <span className="text-sm text-gray-500">
                          {meeting.meetingDate.toDate().toLocaleDateString(lang === 'ar' ? 'ar-SA' : 'en-US')}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setMeetingModal(null)}>
                <X className="w-4 h-4 mr-2" />
                {isRTL ? "إلغاء" : "Cancel"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Meeting Details Modal */}
      <Dialog open={!!meetingDetailsModal} onOpenChange={() => setMeetingDetailsModal(null)}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5 text-blue-600" />
              {isRTL ? "تفاصيل الاجتماع" : "Meeting Details"}
            </DialogTitle>
          </DialogHeader>
          {meetingDetailsModal && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    {isRTL ? "عنوان الاجتماع" : "Meeting Title"}
                  </label>
                  <p className="text-sm text-gray-900 mt-1">{meetingDetailsModal.meeting.title}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    {isRTL ? "التاريخ والوقت" : "Date & Time"}
                  </label>
                  <p className="text-sm text-gray-900 mt-1">
                    {meetingDetailsModal.meeting.meetingDate.toDate().toLocaleString(lang === 'ar' ? 'ar-SA' : 'en-US')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    {isRTL ? "المدة" : "Duration"}
                  </label>
                  <p className="text-sm text-gray-900 mt-1">
                    {meetingDetailsModal.meeting.duration} {isRTL ? "دقيقة" : "minutes"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    {isRTL ? "الحالة" : "Status"}
                  </label>
                  <p className="text-sm text-gray-900 mt-1 capitalize">{meetingDetailsModal.meeting.status}</p>
                </div>
              </div>

              {meetingDetailsModal.meeting.description && (
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    {isRTL ? "الوصف" : "Description"}
                  </label>
                  <p className="text-sm text-gray-900 mt-1">{meetingDetailsModal.meeting.description}</p>
                </div>
              )}

              {meetingDetailsModal.meeting.attendees && meetingDetailsModal.meeting.attendees.length > 0 && (
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    {isRTL ? "الحضور" : "Attendees"}
                  </label>
                  <div className="mt-2 space-y-2">
                    {meetingDetailsModal.meeting.attendees.map((attendee, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                        <span className="font-medium">{attendee.name}</span>
                        <span className="text-gray-500">({attendee.email})</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {meetingDetailsModal.meeting.summary && (
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    {isRTL ? "ملخص الاجتماع" : "Meeting Summary"}
                  </label>
                  <p className="text-sm text-gray-900 mt-1">{meetingDetailsModal.meeting.summary}</p>
                </div>
              )}

              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => {
                    if (meetingDetailsModal?.systemId) {
                      handleUnlinkMeeting(meetingDetailsModal.systemId);
                    }
                  }}
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  <Unlink className="w-4 h-4 mr-2" />
                  {isRTL ? "إلغاء الربط" : "Unlink"}
                </Button>

                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => setMeetingDetailsModal(null)}>
                    <X className="w-4 h-4 mr-2" />
                    {isRTL ? "إغلاق" : "Close"}
                  </Button>
                  <Button
                    onClick={() => {
                      setMeetingDetailsModal(null);
                      setMeetingModal({
                        systemId: meetingDetailsModal?.systemId || '',
                        currentMeetingId: meetingDetailsModal?.meeting.id
                      });
                    }}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Users className="w-4 h-4 mr-2" />
                    {isRTL ? "تعديل الربط" : "Edit Link"}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Link Attachment Modal */}
      <Dialog open={!!linkModal} onOpenChange={() => setLinkModal(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="pb-4">
            <DialogTitle className="text-lg font-semibold flex items-center gap-2">
              <Link className="w-5 h-5 text-blue-600" />
              {isRTL ? "إرفاق رابط" : "Attach Link"}
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-1">
              {isRTL ? "أضف رابطاً مرجعياً لهذه المهمة" : "Add a reference link for this task"}
            </p>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                {isRTL ? "الرابط (URL)" : "Link (URL)"}
              </label>
              <input
                type="url"
                value={tempLink}
                onChange={(e) => setTempLink(e.target.value)}
                placeholder={isRTL ? "https://example.com" : "https://example.com"}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              {tempLink && (
                <div className="text-xs text-gray-500">
                  {isRTL ? "معاينة:" : "Preview:"}
                  <a
                    href={tempLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline ml-1"
                  >
                    {tempLink.length > 50 ? `${tempLink.substring(0, 50)}...` : tempLink}
                  </a>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => {
                setLinkModal(null);
                setTempLink('');
              }}
              className="flex-1"
            >
              {isRTL ? "إلغاء" : "Cancel"}
            </Button>
            <Button
              onClick={handleLinkSave}
              disabled={!tempLink.trim()}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Save className="w-4 h-4 mr-2" />
              {isRTL ? "حفظ الرابط" : "Save Link"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Date Edit Modal */}
      <Dialog open={!!dateEditModal} onOpenChange={() => setDateEditModal(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="pb-4">
            <DialogTitle className="text-lg font-semibold flex items-center gap-2">
              <CalendarDays className="w-5 h-5 text-green-600" />
              {isRTL ? "تعديل التواريخ" : "Edit Dates"}
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-1">
              {dateEditModal?.taskType === 'classificationDataStatus' || dateEditModal?.taskType === 'finalApprovalStatus'
                ? (isRTL ? "تعديل تاريخ المهمة" : "Edit task date")
                : (isRTL ? "تعديل تاريخ البداية والنهاية للمهمة" : "Edit start and end dates for this task")
              }
            </p>
          </DialogHeader>
          <div className="space-y-4">
            {dateEditModal?.taskType === 'classificationDataStatus' || dateEditModal?.taskType === 'finalApprovalStatus' ? (
              // Single date for Classification and Final Approval
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  {isRTL ? "التاريخ" : "Date"}
                </label>
                <Input
                  type="date"
                  value={tempStartDate}
                  onChange={(e) => setTempStartDate(e.target.value)}
                  className="border-gray-200 focus:border-green-400 focus:ring-green-400"
                />
              </div>
            ) : (
              // Start and end dates for other tasks
              <>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    {isRTL ? "تاريخ البداية" : "Start Date"}
                  </label>
                  <Input
                    type="datetime-local"
                    value={tempStartDate}
                    onChange={(e) => setTempStartDate(e.target.value)}
                    className="border-gray-200 focus:border-green-400 focus:ring-green-400"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    {isRTL ? "تاريخ النهاية" : "End Date"}
                  </label>
                  <Input
                    type="datetime-local"
                    value={tempEndDate}
                    onChange={(e) => setTempEndDate(e.target.value)}
                    className="border-gray-200 focus:border-green-400 focus:ring-green-400"
                  />
                </div>
              </>
            )}
            <div className="flex justify-end gap-3 pt-2">
              <Button variant="outline" onClick={() => setDateEditModal(null)} className="px-4">
                <X className="w-4 h-4 mr-2" />
                {isRTL ? "إلغاء" : "Cancel"}
              </Button>
              <Button onClick={handleDateSave} className="px-4 bg-green-600 hover:bg-green-700">
                <Save className="w-4 h-4 mr-2" />
                {isRTL ? "حفظ التواريخ" : "Save Dates"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Approval Delay Reason Modal with Timeline */}
      <Dialog open={!!approvalDelayModal} onOpenChange={() => setApprovalDelayModal(null)}>
        <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader className="pb-4 flex-shrink-0">
            <DialogTitle className="text-lg font-semibold flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-red-600" />
              {isRTL ? "إدارة تأخيرات الموافقة" : "Manage Approval Delays"}
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-1">
              {approvalDelayModal && (
                <>
                  {isRTL
                    ? "عرض التاريخ وإضافة سبب تأخير جديد"
                    : "View timeline and add new delay reason"
                  }
                </>
              )}
            </p>
          </DialogHeader>

          <div className="flex-1 overflow-hidden flex gap-6">
            {/* Left Side - Existing Timeline */}
            {approvalDelayModal && (() => {
              const currentSystem = systems.find(s => s.id === approvalDelayModal.systemId);
              const existingHistory = currentSystem?.finalApprovalDelayHistory || [];

              return existingHistory.length > 0 ? (
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <Clock className="w-4 h-4 text-orange-600" />
                    {isRTL ? "التأخيرات السابقة" : "Previous Delays"}
                  </h3>

                  <div className="max-h-80 overflow-y-auto pr-2">
                    <div className="relative">
                      {/* Timeline Line */}
                      <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-red-200"></div>

                      {/* Timeline Entries */}
                      <div className="space-y-4">
                        {existingHistory.map((entry, index) => (
                          <div key={entry.id} className="relative flex items-start gap-3">
                            {/* Timeline Dot */}
                            <div className="relative z-10 flex-shrink-0">
                              <div className="w-8 h-8 bg-red-100 border-2 border-red-200 rounded-full flex items-center justify-center">
                                <span className="text-xs font-bold text-red-600">{index + 1}</span>
                              </div>
                            </div>

                            {/* Timeline Content */}
                            <div className="flex-1 min-w-0 pb-4">
                              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                                <div className="flex items-start justify-between mb-2">
                                  <span className="text-xs font-medium text-red-700">
                                    {entry.createdAt.toDate().toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
                                      month: 'short',
                                      day: 'numeric',
                                      hour: '2-digit',
                                      minute: '2-digit'
                                    })}
                                  </span>
                                  <div className="flex gap-1">
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => {
                                        setEditingDelayEntry({
                                          entryId: entry.id,
                                          reason: entry.reason
                                        });
                                      }}
                                      className="h-5 w-5 p-0 text-red-600 hover:text-red-800 hover:bg-red-100"
                                      title={isRTL ? "تعديل السبب" : "Edit reason"}
                                    >
                                      <FileText className="w-3 h-3" />
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => {
                                        if (approvalDelayModal && confirm(isRTL ? "هل أنت متأكد من حذف هذا السبب؟" : "Are you sure you want to delete this delay reason?")) {
                                          handleDelayEntryDelete(approvalDelayModal.systemId, entry.id);
                                        }
                                      }}
                                      className="h-5 w-5 p-0 text-red-600 hover:text-red-800 hover:bg-red-100"
                                      title={isRTL ? "حذف السبب" : "Delete reason"}
                                    >
                                      <Trash2 className="w-3 h-3" />
                                    </Button>
                                  </div>
                                </div>
                                {editingDelayEntry?.entryId === entry.id ? (
                                  <div className="space-y-2">
                                    <Textarea
                                      value={editingDelayEntry.reason}
                                      onChange={(e) => setEditingDelayEntry({
                                        ...editingDelayEntry,
                                        reason: e.target.value
                                      })}
                                      rows={2}
                                      className="text-xs resize-none border-red-300 focus:border-red-400 focus:ring-red-400"
                                    />
                                    <div className="flex gap-1">
                                      <Button
                                        size="sm"
                                        onClick={handleDelayEntryEditSave}
                                        className="h-6 px-2 text-xs bg-red-600 hover:bg-red-700"
                                      >
                                        <Save className="w-3 h-3 mr-1" />
                                        {isRTL ? "حفظ" : "Save"}
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => setEditingDelayEntry(null)}
                                        className="h-6 px-2 text-xs"
                                      >
                                        <X className="w-3 h-3 mr-1" />
                                        {isRTL ? "إلغاء" : "Cancel"}
                                      </Button>
                                    </div>
                                  </div>
                                ) : (
                                  <p className="text-xs text-red-800 leading-relaxed">
                                    {entry.reason}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex-1 min-w-0 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <Clock className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                    <p className="text-sm">
                      {isRTL ? "لا توجد تأخيرات سابقة" : "No previous delays"}
                    </p>
                  </div>
                </div>
              );
            })()}

            {/* Right Side - Add New Delay */}
            <div className="w-80 flex-shrink-0 border-l border-gray-200 pl-6">
              <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Plus className="w-4 h-4 text-red-600" />
                {isRTL ? "إضافة تأخير جديد" : "Add New Delay"}
              </h3>

              <div className="space-y-4">
                {/* Current Date Display */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Calendar className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">
                      {isRTL ? "تاريخ اليوم" : "Today's Date"}
                    </span>
                  </div>
                  <p className="text-sm text-blue-800">
                    {new Date().toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>

                {/* Delay Reason Input */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    {isRTL ? "سبب التأخير" : "Delay Reason"}
                  </label>
                  <Textarea
                    value={tempDelayReason}
                    onChange={(e) => setTempDelayReason(e.target.value)}
                    placeholder={isRTL ? "أدخل سبب تأخير الموافقة..." : "Enter the reason for approval delay..."}
                    rows={4}
                    className="resize-none border-gray-200 focus:border-red-400 focus:ring-red-400"
                  />
                  <div className="text-xs text-gray-500 text-right">
                    {tempDelayReason.split(' ').filter(word => word.length > 0).length}/50 {isRTL ? "كلمة" : "words"}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col gap-2 pt-2">
                  <Button
                    onClick={handleApprovalDelayReasonSave}
                    className="w-full bg-red-600 hover:bg-red-700"
                    disabled={!tempDelayReason.trim()}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {isRTL ? "إضافة إلى التاريخ" : "Add to Timeline"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setApprovalDelayModal(null)}
                    className="w-full"
                  >
                    <X className="w-4 h-4 mr-2" />
                    {isRTL ? "إلغاء" : "Cancel"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delay History Modal with Timeline UI */}
      <Dialog open={!!delayHistoryModal} onOpenChange={() => setDelayHistoryModal(null)}>
        <DialogContent className="sm:max-w-3xl">
          <DialogHeader className="pb-4">
            <DialogTitle className="text-lg font-semibold flex items-center gap-2">
              <Clock className="w-5 h-5 text-orange-600" />
              {isRTL ? "تاريخ تأخيرات الموافقة" : "Approval Delay Timeline"}
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-1">
              {delayHistoryModal && (
                <>
                  {isRTL
                    ? `تاريخ التأخيرات للنظام: ${delayHistoryModal.systemName}`
                    : `Delay timeline for system: ${delayHistoryModal.systemName}`
                  }
                </>
              )}
            </p>
          </DialogHeader>

          {/* Timeline Container */}
          <div className="max-h-96 overflow-y-auto px-4">
            <div className="relative">
              {/* Timeline Line */}
              <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gradient-to-b from-red-200 via-orange-200 to-green-200"></div>

              {/* Timeline Entries */}
              <div className="space-y-6">
                {delayHistoryModal?.delayHistory.map((entry, index) => (
                  <div key={entry.id} className="relative flex items-start gap-4">
                    {/* Timeline Dot */}
                    <div className="relative z-10 flex-shrink-0">
                      <div className="w-12 h-12 bg-red-100 border-4 border-red-200 rounded-full flex items-center justify-center">
                        <AlertTriangle className="w-5 h-5 text-red-600" />
                      </div>
                      {/* Entry Number */}
                      <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-600 text-white text-xs font-bold rounded-full flex items-center justify-center">
                        {index + 1}
                      </div>
                    </div>

                    {/* Timeline Content */}
                    <div className="flex-1 min-w-0 pb-6">
                      <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-4">
                        {/* Header */}
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <h4 className="text-sm font-semibold text-gray-900">
                              {isRTL ? "سبب التأخير" : "Delay Reason"}
                            </h4>
                            <span className="px-2 py-1 bg-red-100 text-red-700 text-xs font-medium rounded-full">
                              {isRTL ? "تأخير" : "Delayed"}
                            </span>
                          </div>
                          <div className="text-right">
                            <div className="text-xs font-medium text-gray-900">
                              {entry.createdAt.toDate().toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
                                weekday: 'short',
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })}
                            </div>
                            <div className="text-xs text-gray-500">
                              {entry.createdAt.toDate().toLocaleTimeString(isRTL ? 'ar-SA' : 'en-US', {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </div>
                          </div>
                        </div>

                        {/* Delay Reason Content */}
                        <div className="bg-gray-50 rounded-md p-3 mb-3">
                          {editingDelayEntry?.entryId === entry.id ? (
                            <div className="space-y-2">
                              <Textarea
                                value={editingDelayEntry.reason}
                                onChange={(e) => setEditingDelayEntry({
                                  ...editingDelayEntry,
                                  reason: e.target.value
                                })}
                                rows={3}
                                className="text-sm resize-none border-gray-300 focus:border-red-400 focus:ring-red-400"
                              />
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  onClick={async () => {
                                    if (!delayHistoryModal) return;
                                    try {
                                      await SystemsService.updateApprovalDelayEntry(
                                        delayHistoryModal.systemId,
                                        editingDelayEntry.entryId,
                                        editingDelayEntry.reason.trim()
                                      );
                                      await loadData();
                                      // Update the modal data
                                      const updatedSystem = systems.find(s => s.id === delayHistoryModal.systemId);
                                      if (updatedSystem) {
                                        setDelayHistoryModal({
                                          ...delayHistoryModal,
                                          delayHistory: updatedSystem.finalApprovalDelayHistory || []
                                        });
                                      }
                                      setEditingDelayEntry(null);
                                      toast({
                                        title: isRTL ? "تم تحديث التأخير" : "Delay Updated",
                                        description: isRTL ? "تم تحديث سبب التأخير بنجاح" : "Delay reason updated successfully",
                                      });
                                    } catch {
                                      toast({
                                        title: isRTL ? "خطأ في التحديث" : "Update Error",
                                        description: isRTL ? "فشل في تحديث سبب التأخير" : "Failed to update delay reason",
                                        variant: "destructive",
                                      });
                                    }
                                  }}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  <Save className="w-4 h-4 mr-2" />
                                  {isRTL ? "حفظ" : "Save"}
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => setEditingDelayEntry(null)}
                                >
                                  <X className="w-4 h-4 mr-2" />
                                  {isRTL ? "إلغاء" : "Cancel"}
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="flex items-start justify-between">
                              <p className="text-sm text-gray-700 leading-relaxed flex-1">
                                {entry.reason}
                              </p>
                              <div className="flex gap-1 ml-2">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => {
                                    setEditingDelayEntry({
                                      entryId: entry.id,
                                      reason: entry.reason
                                    });
                                  }}
                                  className="h-6 w-6 p-0 text-gray-500 hover:text-red-600 hover:bg-red-50"
                                  title={isRTL ? "تعديل السبب" : "Edit reason"}
                                >
                                  <FileText className="w-3 h-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={async () => {
                                    if (delayHistoryModal && confirm(isRTL ? "هل أنت متأكد من حذف هذا السبب؟" : "Are you sure you want to delete this delay reason?")) {
                                      try {
                                        await SystemsService.deleteApprovalDelayEntry(delayHistoryModal.systemId, entry.id);
                                        await loadData();
                                        // Update the modal data
                                        const updatedSystem = systems.find(s => s.id === delayHistoryModal.systemId);
                                        if (updatedSystem) {
                                          const updatedHistory = updatedSystem.finalApprovalDelayHistory || [];
                                          if (updatedHistory.length === 0) {
                                            // If no delays remain, close the modal
                                            setDelayHistoryModal(null);
                                          } else {
                                            setDelayHistoryModal({
                                              ...delayHistoryModal,
                                              delayHistory: updatedHistory
                                            });
                                          }
                                        }
                                        toast({
                                          title: isRTL ? "تم حذف التأخير" : "Delay Deleted",
                                          description: isRTL ? "تم حذف سبب التأخير بنجاح" : "Delay reason deleted successfully",
                                        });
                                      } catch {
                                        toast({
                                          title: isRTL ? "خطأ في الحذف" : "Delete Error",
                                          description: isRTL ? "فشل في حذف سبب التأخير" : "Failed to delete delay reason",
                                          variant: "destructive",
                                        });
                                      }
                                    }
                                  }}
                                  className="h-6 w-6 p-0 text-gray-500 hover:text-red-600 hover:bg-red-50"
                                  title={isRTL ? "حذف السبب" : "Delete reason"}
                                >
                                  <Trash2 className="w-3 h-3" />
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Footer */}
                        {entry.createdByName && (
                          <div className="flex items-center gap-2 text-xs text-gray-500">
                            <User className="w-3 h-3" />
                            <span>{isRTL ? "بواسطة:" : "Added by:"} {entry.createdByName}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                {/* Final Approval Entry (if status is DONE) */}
                {delayHistoryModal && (
                  <div className="relative flex items-start gap-4">
                    {/* Timeline Dot - Success */}
                    <div className="relative z-10 flex-shrink-0">
                      <div className="w-12 h-12 bg-green-100 border-4 border-green-200 rounded-full flex items-center justify-center">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      </div>
                    </div>

                    {/* Final Status Content */}
                    <div className="flex-1 min-w-0">
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="text-sm font-semibold text-green-900">
                            {isRTL ? "تم استلام الموافقة النهائية" : "Final Approval Received"}
                          </h4>
                          <span className="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">
                            {isRTL ? "مكتمل" : "Completed"}
                          </span>
                        </div>
                        <p className="text-xs text-green-700">
                          {isRTL ? "تم حل جميع التأخيرات واستلام الموافقة النهائية" : "All delays resolved and final approval received"}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end pt-4 border-t">
            <Button variant="outline" onClick={() => setDelayHistoryModal(null)} className="px-4">
              <X className="w-4 h-4 mr-2" />
              {isRTL ? "إغلاق" : "Close"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Bulk Update Modal */}
      <Dialog open={!!bulkUpdateModal} onOpenChange={() => setBulkUpdateModal(null)}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader className="pb-4">
            <DialogTitle className="text-lg font-semibold flex items-center gap-2">
              <Users className="w-5 h-5 text-blue-600" />
              {isRTL ? "تحديث مجمع للمجموعة" : "Bulk Update Group"}
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-1">
              {bulkUpdateModal && (
                <>
                  {isRTL
                    ? `تحديث جميع الأنظمة في المجموعة ${bulkUpdateModal.groupNumber} لمهمة: ${taskTypes.find(t => t.key === bulkUpdateModal.taskType)?.label.ar}`
                    : `Update all systems in Group ${bulkUpdateModal.groupNumber} for task: ${taskTypes.find(t => t.key === bulkUpdateModal.taskType)?.label.en}`
                  }
                </>
              )}
            </p>
          </DialogHeader>
          <div className="space-y-4">
            {/* Status Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                {isRTL ? "الحالة" : "Status"}
              </label>
              <Select value={bulkStatus} onValueChange={(value) => setBulkStatus(value as SystemTaskStatus)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {bulkUpdateModal && taskTypes.find(t => t.key === bulkUpdateModal.taskType)?.statuses.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label[lang as 'en' | 'ar']}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Date Fields */}
            {bulkUpdateModal?.taskType === 'classificationDataStatus' || bulkUpdateModal?.taskType === 'finalApprovalStatus' ? (
              // Single date for Classification and Final Approval
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  {isRTL ? "التاريخ" : "Date"}
                </label>
                <Input
                  type="date"
                  value={bulkStartDate}
                  onChange={(e) => setBulkStartDate(e.target.value)}
                  className="border-gray-200 focus:border-blue-400 focus:ring-blue-400"
                />
              </div>
            ) : (
              // Start and end dates for other tasks
              <>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    {isRTL ? "تاريخ البداية" : "Start Date"}
                  </label>
                  <Input
                    type="date"
                    value={bulkStartDate}
                    onChange={(e) => setBulkStartDate(e.target.value)}
                    className="border-gray-200 focus:border-blue-400 focus:ring-blue-400"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    {isRTL ? "تاريخ النهاية" : "End Date"}
                  </label>
                  <Input
                    type="date"
                    value={bulkEndDate}
                    onChange={(e) => setBulkEndDate(e.target.value)}
                    className="border-gray-200 focus:border-blue-400 focus:ring-blue-400"
                  />
                </div>
              </>
            )}

            {/* Link Field */}
            {taskTypes.find(t => t.key === bulkUpdateModal?.taskType)?.hasLink && (
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  {isRTL ? "الرابط" : "Link"}
                </label>
                <Input
                  type="url"
                  value={bulkLink}
                  onChange={(e) => setBulkLink(e.target.value)}
                  placeholder={isRTL ? "أدخل الرابط..." : "Enter link..."}
                  className="border-gray-200 focus:border-blue-400 focus:ring-blue-400"
                />
              </div>
            )}

            {/* Note Field */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                {isRTL ? "ملاحظة" : "Note"}
              </label>
              <Textarea
                value={bulkNote}
                onChange={(e) => setBulkNote(e.target.value)}
                placeholder={isRTL ? "أدخل ملاحظة..." : "Enter note..."}
                rows={3}
                className="resize-none border-gray-200 focus:border-blue-400 focus:ring-blue-400"
              />
            </div>

            <div className="flex justify-end gap-3 pt-2">
              <Button variant="outline" onClick={() => setBulkUpdateModal(null)} className="px-4">
                <X className="w-4 h-4 mr-2" />
                {isRTL ? "إلغاء" : "Cancel"}
              </Button>
              <Button
                onClick={handleBulkUpdate}
                disabled={isBulkUpdating}
                className="px-4 bg-blue-600 hover:bg-blue-700"
              >
                {isBulkUpdating ? (
                  <Loader className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Users className="w-4 h-4 mr-2" />
                )}
                {isBulkUpdating
                  ? (isRTL ? "جاري التحديث..." : "Updating...")
                  : (isRTL ? "تحديث المجموعة" : "Update Group")
                }
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Progress Breakdown Modal */}
      <Dialog open={progressBreakdownModal} onOpenChange={setProgressBreakdownModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3 text-xl font-bold">
              <div className="w-8 h-8 bg-gradient-to-br from-[var(--brand-blue)] to-blue-600 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-white" />
              </div>
              {isRTL ? "تفصيل نسبة الإنجاز الإجمالية" : "Overall Completion Breakdown"}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Overall Summary */}
            <div className="bg-gradient-to-br from-[var(--brand-blue)]/10 to-blue-50 rounded-xl p-6 border border-[var(--brand-blue)]/20">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-bold text-gray-900">
                    {isRTL ? "الملخص الإجمالي" : "Overall Summary"}
                  </h3>
                  <p className="text-gray-600">
                    {isRTL ? "نظرة عامة على تقدم جميع المهام" : "Overview of all task progress"}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-[var(--brand-blue)]">
                    {stats.overallCompletionPercentage}%
                  </div>
                  <div className="text-sm text-gray-600">
                    {stats.completedTasks} / {stats.totalTasks} {isRTL ? "مهمة مكتملة" : "tasks completed"}
                  </div>
                </div>
              </div>

              {/* Overall Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-4 mb-2">
                <div
                  className="bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 h-4 rounded-full transition-all duration-500 flex items-center justify-center"
                  style={{ width: `${stats.overallCompletionPercentage}%` }}
                >
                  <span className="text-xs font-bold text-white">
                    {stats.overallCompletionPercentage}%
                  </span>
                </div>
              </div>
            </div>

            {/* Task Type Breakdown */}
            <div>
              <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-[var(--brand-blue)]" />
                {isRTL ? "تفصيل حسب نوع المهمة" : "Breakdown by Task Type"}
              </h3>

              <div className="grid gap-4">
                {taskTypes.map((taskType) => {
                  const breakdown = stats.taskBreakdown[taskType.key];
                  const statusColor = breakdown.percentage >= 80 ? 'text-green-600' :
                                    breakdown.percentage >= 50 ? 'text-yellow-600' :
                                    'text-red-600';
                  const bgColor = breakdown.percentage >= 80 ? 'bg-green-500' :
                                breakdown.percentage >= 50 ? 'bg-yellow-500' :
                                'bg-red-500';

                  return (
                    <div key={taskType.key} className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all duration-200">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900 mb-1">
                            {taskType.label[lang as 'en' | 'ar']}
                          </h4>
                          <div className="text-sm text-gray-600">
                            {breakdown.completed} / {breakdown.total} {isRTL ? "نظام مكتمل" : "systems completed"}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`text-2xl font-bold ${statusColor}`}>
                            {breakdown.percentage}%
                          </div>
                        </div>
                      </div>

                      {/* Task Progress Bar */}
                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div
                          className={`${bgColor} h-3 rounded-full transition-all duration-500 flex items-center justify-end pr-2`}
                          style={{ width: `${breakdown.percentage}%` }}
                        >
                          {breakdown.percentage > 15 && (
                            <span className="text-xs font-bold text-white">
                              {breakdown.percentage}%
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Systems Completion Status */}
            <div>
              <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                <Database className="w-5 h-5 text-[var(--brand-blue)]" />
                {isRTL ? "حالة إكمال الأنظمة" : "Systems Completion Status"}
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                      <CheckCircle className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">
                        {stats.completedSystems}
                      </div>
                      <div className="text-sm text-green-700">
                        {isRTL ? "أنظمة مكتملة" : "Completed Systems"}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center">
                      <Clock className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-yellow-600">
                        {stats.totalSystems - stats.completedSystems}
                      </div>
                      <div className="text-sm text-yellow-700">
                        {isRTL ? "أنظمة قيد التنفيذ" : "In Progress Systems"}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                      <Database className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-blue-600">
                        {stats.totalSystems}
                      </div>
                      <div className="text-sm text-blue-700">
                        {isRTL ? "إجمالي الأنظمة" : "Total Systems"}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Group Detail Modal */}
      <Dialog open={!!groupDetailModal} onOpenChange={() => setGroupDetailModal(null)}>
        <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto p-4">
          <DialogHeader className="pb-3">
            <DialogTitle className="flex items-center gap-2 text-lg font-bold">
              <div className="w-6 h-6 bg-gradient-to-br from-[var(--brand-blue)] to-blue-600 rounded flex items-center justify-center">
                <BarChart3 className="w-3 h-3 text-white" />
              </div>
              {groupDetailModal?.groupName} - {isRTL ? "تحليل التقدم والأوزان" : "Progress & Weight Analysis"}
            </DialogTitle>
          </DialogHeader>

          {groupDetailModal && (
            <div className="space-y-4">
              {/* Compact Group Summary */}
              <div className="bg-gradient-to-br from-[var(--brand-blue)]/10 to-blue-50 rounded-lg p-4 border border-[var(--brand-blue)]/20">
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-3">
                  {/* Group Info */}
                  <div>
                    <h3 className="text-base font-bold text-gray-900 mb-1">
                      {groupDetailModal.groupName}
                    </h3>
                    <div className="space-y-0.5 text-xs text-gray-600">
                      <div>{groupDetailModal.systems.length} {isRTL ? "نظام" : "systems"}</div>
                      <div>{taskTypes.length} {isRTL ? "مراحل" : "phases"}</div>
                      <div>{groupDetailModal.systems.length * taskTypes.length} {isRTL ? "مهمة" : "tasks"}</div>
                    </div>
                  </div>

                  {/* Progress Stats */}
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[var(--brand-blue)]">
                      {groupDetailModal.progress}%
                    </div>
                    <div className="text-xs text-gray-600">
                      {(() => {
                        const totalTasks = groupDetailModal.systems.length * taskTypes.length;
                        const completedTasks = Math.round((groupDetailModal.progress / 100) * totalTasks);
                        return `${completedTasks}/${totalTasks} ${isRTL ? "مكتمل" : "done"}`;
                      })()}
                    </div>
                  </div>

                  {/* Missing Analysis */}
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {100 - groupDetailModal.progress}%
                    </div>
                    <div className="text-xs text-red-700 font-semibold">
                      {isRTL ? "متبقي" : "Missing"}
                    </div>
                    <div className="text-xs text-gray-600">
                      {(() => {
                        const totalTasks = groupDetailModal.systems.length * taskTypes.length;
                        const remainingTasks = totalTasks - Math.round((groupDetailModal.progress / 100) * totalTasks);
                        return `${remainingTasks} ${isRTL ? "مهمة" : "tasks"}`;
                      })()}
                    </div>
                  </div>

                  {/* Weight Info */}
                  <div className="text-center">
                    <div className="text-sm font-bold text-[var(--brand-blue)]">
                      {(100/taskTypes.length).toFixed(1)}%
                    </div>
                    <div className="text-xs text-gray-600">
                      {isRTL ? "وزن كل مرحلة" : "per phase weight"}
                    </div>
                  </div>
                </div>

                {/* Compact Progress Bar */}
                <div className="w-full bg-red-200 rounded-full h-4 mb-3 relative overflow-hidden">
                  <div
                    className="bg-gradient-to-r from-green-500 to-green-600 h-4 rounded-full transition-all duration-500 flex items-center justify-center relative z-10"
                    style={{ width: `${groupDetailModal.progress}%` }}
                  >
                    {groupDetailModal.progress > 20 && (
                      <span className="text-xs font-bold text-white">
                        {groupDetailModal.progress}%
                      </span>
                    )}
                  </div>
                  {100 - groupDetailModal.progress > 15 && (
                    <div className="absolute right-1 top-1/2 transform -translate-y-1/2 text-xs font-bold text-red-800 z-20">
                      {100 - groupDetailModal.progress}%
                    </div>
                  )}
                </div>
              </div>

              {/* Compact Missing Tasks Analysis */}
              <div className="bg-red-50 rounded-lg p-3 border border-red-200">
                <h3 className="text-sm font-bold text-red-600 mb-2 flex items-center gap-1">
                  <AlertTriangle className="w-4 h-4" />
                  {isRTL ? "المهام المتبقية" : "Missing Tasks"}
                </h3>
                <div className="grid grid-cols-3 gap-3 text-center">
                  {(() => {
                    const totalTasks = groupDetailModal.systems.length * taskTypes.length;
                    const completedTasks = Math.round((groupDetailModal.progress / 100) * totalTasks);
                    const missingTasks = totalTasks - completedTasks;
                    const missingWeight = 100 - groupDetailModal.progress;

                    return (
                      <>
                        <div>
                          <div className="text-lg font-bold text-red-600">{missingTasks}</div>
                          <div className="text-xs text-red-700">{isRTL ? "مهام" : "Tasks"}</div>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-red-600">{missingWeight.toFixed(1)}%</div>
                          <div className="text-xs text-red-700">{isRTL ? "وزن" : "Weight"}</div>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-red-600">
                            {(missingWeight / taskTypes.length).toFixed(1)}%
                          </div>
                          <div className="text-xs text-red-700">{isRTL ? "متوسط" : "Avg/Phase"}</div>
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>

              {/* Compact Systems Breakdown */}
              <div>
                <h3 className="text-sm font-bold text-gray-900 mb-2 flex items-center gap-1">
                  <Database className="w-4 h-4 text-[var(--brand-blue)]" />
                  {isRTL ? "تفصيل الأنظمة" : "Systems Breakdown"}
                </h3>

                <div className="space-y-3">
                  {groupDetailModal.systems
                    .sort((a, b) => {
                      // Sort by completion percentage (lowest first to highlight issues)
                      const aCompleted = taskTypes.filter(taskType => {
                        const status = a[taskType.key as keyof System] as SystemTaskStatus;
                        return status === SystemTaskStatus.DONE || status === SystemTaskStatus.DATA_RECEIVED;
                      }).length;
                      const bCompleted = taskTypes.filter(taskType => {
                        const status = b[taskType.key as keyof System] as SystemTaskStatus;
                        return status === SystemTaskStatus.DONE || status === SystemTaskStatus.DATA_RECEIVED;
                      }).length;
                      return aCompleted - bCompleted;
                    })
                    .map((system) => {
                    // Calculate system progress and weights
                    const completedTasks = taskTypes.filter(taskType => {
                      const status = system[taskType.key as keyof System] as SystemTaskStatus;
                      return status === SystemTaskStatus.DONE || status === SystemTaskStatus.DATA_RECEIVED;
                    }).length;
                    const systemProgress = Math.round((completedTasks / taskTypes.length) * 100);
                    const systemWeight = (1 / groupDetailModal.systems.length) * 100; // Equal weight per system
                    const systemContribution = (systemProgress / 100) * systemWeight;
                    const systemMissing = systemWeight - systemContribution;
                    const missingTasks = taskTypes.length - completedTasks;

                    return (
                      <div key={system.id || system.name} className={`rounded-lg border p-3 hover:shadow-sm transition-all duration-200 ${
                        systemProgress < 100 ? 'bg-red-50 border-red-200' : 'bg-green-50 border-green-200'
                      }`}>
                        {/* Compact System Header */}
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-semibold text-gray-900 text-sm truncate">
                                {system.name}
                              </h4>
                              {systemProgress < 100 && (
                                <span className="px-1.5 py-0.5 bg-red-100 text-red-700 text-xs font-semibold rounded">
                                  {isRTL ? "متبقي" : "Missing"}
                                </span>
                              )}
                            </div>
                            <div className="text-xs text-gray-600 space-y-0.5">
                              <div className="truncate"><strong>{isRTL ? "المسؤول:" : "Owner:"}</strong> {system.responsibleOwner}</div>
                              {system.consultantName && (
                                <div className="truncate"><strong>{isRTL ? "المستشار:" : "Consultant:"}</strong> {system.consultantName}</div>
                              )}
                            </div>
                          </div>
                          <div className="text-right ml-3 flex-shrink-0">
                            <div className={`text-xl font-bold ${
                              systemProgress >= 80 ? 'text-green-600' :
                              systemProgress >= 50 ? 'text-yellow-600' :
                              'text-red-600'
                            }`}>
                              {systemProgress}%
                            </div>
                            <div className="text-xs text-gray-600">
                              {completedTasks}/{taskTypes.length}
                            </div>
                            <div className="bg-white rounded p-1 border mt-1">
                              <div className="text-xs font-bold text-[var(--brand-blue)]">{systemWeight.toFixed(1)}%</div>
                              {systemMissing > 0 && (
                                <div className="text-xs text-red-600 font-semibold">
                                  -{systemMissing.toFixed(1)}%
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Compact Progress Bar */}
                        <div className="w-full bg-red-200 rounded-full h-3 mb-2 relative overflow-hidden">
                          <div
                            className={`h-3 rounded-full transition-all duration-500 ${
                              systemProgress >= 80 ? 'bg-green-500' :
                              systemProgress >= 50 ? 'bg-yellow-500' :
                              'bg-red-500'
                            }`}
                            style={{ width: `${systemProgress}%` }}
                          ></div>
                          {systemProgress < 100 && systemProgress < 85 && (
                            <div className="absolute right-1 top-1/2 transform -translate-y-1/2 text-xs font-bold text-red-800">
                              {100 - systemProgress}%
                            </div>
                          )}
                        </div>

                        {/* Compact Missing Tasks Alert */}
                        {missingTasks > 0 && (
                          <div className="bg-red-100 border border-red-300 rounded p-2 mb-2">
                            <div className="flex items-center gap-1 text-xs text-red-700">
                              <AlertTriangle className="w-3 h-3" />
                              <span className="font-semibold">
                                {missingTasks} {isRTL ? "مهام متبقية" : "missing tasks"} (-{systemMissing.toFixed(1)}%)
                              </span>
                            </div>
                          </div>
                        )}

                        {/* Ultra Compact Task Grid */}
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
                          {taskTypes
                            .sort((a, b) => {
                              // Sort to show incomplete tasks first
                              const aStatus = system[a.key as keyof System] as SystemTaskStatus || SystemTaskStatus.NOT_STARTED;
                              const bStatus = system[b.key as keyof System] as SystemTaskStatus || SystemTaskStatus.NOT_STARTED;
                              const aCompleted = aStatus === SystemTaskStatus.DONE || aStatus === SystemTaskStatus.DATA_RECEIVED;
                              const bCompleted = bStatus === SystemTaskStatus.DONE || bStatus === SystemTaskStatus.DATA_RECEIVED;
                              return aCompleted === bCompleted ? 0 : aCompleted ? 1 : -1;
                            })
                            .map((taskType) => {
                            const status = system[taskType.key as keyof System] as SystemTaskStatus || SystemTaskStatus.NOT_STARTED;
                            const isCompleted = status === SystemTaskStatus.DONE || status === SystemTaskStatus.DATA_RECEIVED;
                            const phaseWeight = (100 / taskTypes.length);

                            return (
                              <div key={taskType.key} className={`p-2 rounded border relative text-center ${
                                isCompleted ? 'bg-green-50 border-green-200' :
                                status === SystemTaskStatus.IN_PROGRESS || status === SystemTaskStatus.DATA_REQUESTED ? 'bg-yellow-50 border-yellow-200' :
                                status === SystemTaskStatus.DATA_SENT_AWAITING_REPLY || status === SystemTaskStatus.MEETING_SCHEDULED ? 'bg-blue-50 border-blue-200' :
                                status === SystemTaskStatus.NO_REPLY_RECEIVED ? 'bg-red-50 border-red-200' :
                                'bg-red-50 border-red-300'
                              }`}>
                                {/* Missing Indicator */}
                                {!isCompleted && (
                                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
                                )}

                                {/* Status Icon */}
                                <div className={`w-5 h-5 mx-auto mb-1 rounded flex items-center justify-center ${
                                  isCompleted ? 'bg-green-500' :
                                  status === SystemTaskStatus.IN_PROGRESS || status === SystemTaskStatus.DATA_REQUESTED ? 'bg-yellow-500' :
                                  status === SystemTaskStatus.DATA_SENT_AWAITING_REPLY || status === SystemTaskStatus.MEETING_SCHEDULED ? 'bg-blue-500' :
                                  status === SystemTaskStatus.NO_REPLY_RECEIVED ? 'bg-red-500' :
                                  'bg-red-400'
                                }`}>
                                  {getStatusIcon(status)}
                                </div>

                                {/* Phase Name */}
                                <div className="text-xs font-medium text-gray-700 leading-tight mb-1 h-8 flex items-center justify-center">
                                  {taskType.label[lang as 'en' | 'ar'].split(' ').slice(0, 2).join(' ')}
                                </div>

                                {/* Weight & Status */}
                                <div className="text-xs">
                                  <div className="font-bold text-[var(--brand-blue)]">
                                    {phaseWeight.toFixed(1)}%
                                  </div>
                                  {!isCompleted && (
                                    <div className="text-red-600 font-semibold">
                                      -{phaseWeight.toFixed(1)}%
                                    </div>
                                  )}
                                  {isCompleted && (
                                    <div className="text-green-600 font-semibold">
                                      ✓
                                    </div>
                                  )}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

          </TabsContent>
        </Tabs>
      </div>

    </div>
  );
}
