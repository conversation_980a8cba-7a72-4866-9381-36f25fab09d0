"use client";

import React, { useState, useEffect } from "react";
import { Locale } from "@/i18n-config";
import { useRouter } from "next/navigation";
import { ArrowLeft, GraduationCap, Lock, AlertTriangle, FileText, Video, Award } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemTasksJourney, PhaseStatus } from "@/components/SystemTasks/SystemTasksJourney";
import { motion } from "framer-motion";

interface KnowledgeTransferPageProps {
  params: Promise<{ lang: Locale; systemId: string }>;
}

export default function KnowledgeTransferPage({ params }: KnowledgeTransferPageProps) {
  const [lang, setLang] = useState<string>('');
  const [systemId, setSystemId] = useState<string>('');

  const router = useRouter();

  // Initialize params
  useEffect(() => {
    params.then(({ lang, systemId }) => {
      setLang(lang);
      setSystemId(systemId);
    });
  }, [params]);

  // Don't render until params are loaded
  if (!lang || !systemId) {
    return <div>Loading...</div>;
  }

  const isRTL = lang === "ar";

  // Mock phase status - Knowledge Transfer is locked
  const phaseStatus: PhaseStatus = {
    dataClassification: { status: 'completed', completionRate: 100 },
    ropa: { status: 'completed', completionRate: 100 },
    dpiaAndTia: { status: 'completed', completionRate: 100 },
    pdplGovernance: { status: 'current', completionRate: 80 },
    knowledgeTransfer: { status: 'locked', completionRate: 0 }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-red-600 to-red-700 text-white">
        <div className="px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/${lang}/Thiqah/Tasks/Systems`)}
                className="text-white hover:bg-white/20"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                {isRTL ? "العودة للأنظمة" : "Back to Systems"}
              </Button>
              <div>
                <h1 className="text-2xl font-bold">
                  {isRTL ? "نقل المعرفة والتوثيق" : "Knowledge Transfer & Documentation"}
                </h1>
                <p className="text-white/90">
                  {isRTL ? "Knowledge Transfer" : "Knowledge Transfer"}
                </p>
              </div>
            </div>
            
            {/* Status Indicator */}
            <div className="flex items-center gap-2 bg-red-500/20 px-4 py-2 rounded-lg">
              <Lock className="w-5 h-5" />
              <span className="font-medium">
                {isRTL ? "مقفل" : "Locked"}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Journey Navigation */}
      <div className="bg-white border-b border-gray-200 px-8 py-6">
        <SystemTasksJourney
          lang={lang}
          systemId={systemId}
          phaseStatus={phaseStatus}
          currentPhase="knowledgeTransfer"
          showNavigation={false}
        />
      </div>

      {/* Locked Content */}
      <div className="px-8 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-2xl mx-auto text-center"
        >
          {/* Lock Icon */}
          <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Lock className="w-12 h-12 text-red-400" />
          </div>

          {/* Title */}
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {isRTL ? "هذه المرحلة مقفلة" : "This Phase is Locked"}
          </h2>

          {/* Description */}
          <p className="text-lg text-gray-600 mb-8">
            {isRTL 
              ? "يجب إكمال مرحلة حوكمة قانون حماية البيانات بنسبة 100% قبل الوصول إلى مرحلة نقل المعرفة النهائية"
              : "You must complete the PDPL Governance phase at 100% before accessing the final Knowledge Transfer phase"
            }
          </p>

          {/* Requirements Card */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <div className="flex items-start gap-4">
              <AlertTriangle className="w-6 h-6 text-red-500 flex-shrink-0 mt-1" />
              <div className="text-left">
                <h3 className="font-semibold text-gray-900 mb-2">
                  {isRTL ? "متطلبات الوصول" : "Access Requirements"}
                </h3>
                <ul className="text-gray-600 space-y-2">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                    {isRTL ? "إكمال جميع سياسات حماية البيانات" : "Complete all data protection policies"}
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                    {isRTL ? "تحديد الأدوار والمسؤوليات" : "Define roles and responsibilities"}
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                    {isRTL ? "وضع إجراءات الاستجابة للحوادث" : "Establish incident response procedures"}
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                    {isRTL ? "تأكيد الامتثال القانوني الكامل" : "Confirm full legal compliance"}
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* What's Coming Next */}
          <div className="bg-gradient-to-r from-red-50 to-orange-50 rounded-xl p-6 mb-8">
            <div className="flex items-center gap-3 mb-4">
              <GraduationCap className="w-6 h-6 text-red-600" />
              <h3 className="text-lg font-semibold text-gray-900">
                {isRTL ? "ما ينتظرك في هذه المرحلة" : "What Awaits in This Phase"}
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-left">
              <div className="bg-white rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="w-5 h-5 text-red-600" />
                  <h4 className="font-medium text-gray-900">
                    {isRTL ? "التوثيق الشامل" : "Comprehensive Documentation"}
                  </h4>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• {isRTL ? "دليل المستخدم النهائي" : "End-user manual"}</li>
                  <li>• {isRTL ? "وثائق العمليات" : "Process documentation"}</li>
                  <li>• {isRTL ? "إرشادات الصيانة" : "Maintenance guidelines"}</li>
                </ul>
              </div>
              <div className="bg-white rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Video className="w-5 h-5 text-red-600" />
                  <h4 className="font-medium text-gray-900">
                    {isRTL ? "التدريب والتأهيل" : "Training & Qualification"}
                  </h4>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• {isRTL ? "جلسات تدريبية" : "Training sessions"}</li>
                  <li>• {isRTL ? "مواد تعليمية" : "Educational materials"}</li>
                  <li>• {isRTL ? "اختبارات الكفاءة" : "Competency tests"}</li>
                </ul>
              </div>
              <div className="bg-white rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Award className="w-5 h-5 text-red-600" />
                  <h4 className="font-medium text-gray-900">
                    {isRTL ? "التسليم والاعتماد" : "Handover & Certification"}
                  </h4>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• {isRTL ? "تسليم رسمي للنظام" : "Formal system handover"}</li>
                  <li>• {isRTL ? "شهادات الإنجاز" : "Completion certificates"}</li>
                  <li>• {isRTL ? "خطة الدعم المستقبلي" : "Future support plan"}</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Completion Celebration */}
          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 mb-8 border border-green-200">
            <div className="text-center">
              <Award className="w-12 h-12 text-green-600 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {isRTL ? "🎉 المرحلة النهائية!" : "🎉 Final Phase!"}
              </h3>
              <p className="text-gray-600">
                {isRTL 
                  ? "بإكمال هذه المرحلة، ستكون قد أنجزت جميع متطلبات النظام بنجاح وحصلت على شهادة الإنجاز الكاملة"
                  : "By completing this phase, you will have successfully accomplished all system requirements and earned the complete achievement certificate"
                }
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="outline"
              onClick={() => router.push(`/${lang}/Thiqah/Tasks/Systems/${systemId}/PDPL-Governance`)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              {isRTL ? "العودة إلى PDPL Governance" : "Return to PDPL Governance"}
            </Button>
            <Button
              onClick={() => router.push(`/${lang}/Thiqah/Tasks/Systems`)}
              className="bg-red-600 hover:bg-red-700"
            >
              {isRTL ? "العودة إلى لوحة الأنظمة" : "Back to Systems Dashboard"}
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
