"use client";

import React, { useState, useEffect } from "react";
import { Locale } from "@/i18n-config";
import { useRouter } from "next/navigation";
import { ArrowLeft, BookOpen, Lock, AlertTriangle, Scale, FileCheck, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemTasksJourney, PhaseStatus } from "@/components/SystemTasks/SystemTasksJourney";
import { motion } from "framer-motion";

interface PDPLGovernancePageProps {
  params: Promise<{ lang: Locale; systemId: string }>;
}

export default function PDPLGovernancePage({ params }: PDPLGovernancePageProps) {
  const [lang, setLang] = useState<string>('');
  const [systemId, setSystemId] = useState<string>('');

  const router = useRouter();

  // Initialize params
  useEffect(() => {
    params.then(({ lang, systemId }) => {
      setLang(lang);
      setSystemId(systemId);
    });
  }, [params]);

  // Don't render until params are loaded
  if (!lang || !systemId) {
    return <div>Loading...</div>;
  }

  const isRTL = lang === "ar";

  // Mock phase status - PDPL Governance is locked
  const phaseStatus: PhaseStatus = {
    dataClassification: { status: 'completed', completionRate: 100 },
    ropa: { status: 'completed', completionRate: 100 },
    dpiaAndTia: { status: 'current', completionRate: 60 },
    pdplGovernance: { status: 'locked', completionRate: 0 },
    knowledgeTransfer: { status: 'locked', completionRate: 0 }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-purple-700 text-white">
        <div className="px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/${lang}/Thiqah/Tasks/Systems`)}
                className="text-white hover:bg-white/20"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                {isRTL ? "العودة للأنظمة" : "Back to Systems"}
              </Button>
              <div>
                <h1 className="text-2xl font-bold">
                  {isRTL ? "حوكمة قانون حماية البيانات الشخصية" : "Personal Data Protection Law Governance"}
                </h1>
                <p className="text-white/90">
                  {isRTL ? "PDPL Governance" : "PDPL Governance"}
                </p>
              </div>
            </div>
            
            {/* Status Indicator */}
            <div className="flex items-center gap-2 bg-red-500/20 px-4 py-2 rounded-lg">
              <Lock className="w-5 h-5" />
              <span className="font-medium">
                {isRTL ? "مقفل" : "Locked"}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Journey Navigation */}
      <div className="bg-white border-b border-gray-200 px-8 py-6">
        <SystemTasksJourney
          lang={lang}
          systemId={systemId}
          phaseStatus={phaseStatus}
          currentPhase="pdplGovernance"
          showNavigation={false}
        />
      </div>

      {/* Locked Content */}
      <div className="px-8 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-2xl mx-auto text-center"
        >
          {/* Lock Icon */}
          <div className="w-24 h-24 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Lock className="w-12 h-12 text-purple-400" />
          </div>

          {/* Title */}
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {isRTL ? "هذه المرحلة مقفلة" : "This Phase is Locked"}
          </h2>

          {/* Description */}
          <p className="text-lg text-gray-600 mb-8">
            {isRTL 
              ? "يجب إكمال مرحلة تقييم الأثر وتحليل التهديدات بنسبة 100% قبل الوصول إلى مرحلة حوكمة قانون حماية البيانات"
              : "You must complete the DPIA & TIA phase at 100% before accessing the PDPL Governance phase"
            }
          </p>

          {/* Requirements Card */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <div className="flex items-start gap-4">
              <AlertTriangle className="w-6 h-6 text-purple-500 flex-shrink-0 mt-1" />
              <div className="text-left">
                <h3 className="font-semibold text-gray-900 mb-2">
                  {isRTL ? "متطلبات الوصول" : "Access Requirements"}
                </h3>
                <ul className="text-gray-600 space-y-2">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                    {isRTL ? "إكمال تقييم أثر حماية البيانات (DPIA)" : "Complete Data Protection Impact Assessment (DPIA)"}
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                    {isRTL ? "إنهاء تحليل تأثير التهديدات (TIA)" : "Finish Threat Impact Analysis (TIA)"}
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                    {isRTL ? "تحديد تدابير الحماية المطلوبة" : "Define required protection measures"}
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                    {isRTL ? "وضع خطط التخفيف من المخاطر" : "Establish risk mitigation plans"}
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* What's Coming Next */}
          <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-6 mb-8">
            <div className="flex items-center gap-3 mb-4">
              <BookOpen className="w-6 h-6 text-purple-600" />
              <h3 className="text-lg font-semibold text-gray-900">
                {isRTL ? "ما ينتظرك في هذه المرحلة" : "What Awaits in This Phase"}
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-left">
              <div className="bg-white rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Scale className="w-5 h-5 text-purple-600" />
                  <h4 className="font-medium text-gray-900">
                    {isRTL ? "الامتثال القانوني" : "Legal Compliance"}
                  </h4>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• {isRTL ? "مراجعة متطلبات PDPL" : "PDPL requirements review"}</li>
                  <li>• {isRTL ? "تقييم الامتثال الحالي" : "Current compliance assessment"}</li>
                  <li>• {isRTL ? "خطة تحسين الامتثال" : "Compliance improvement plan"}</li>
                </ul>
              </div>
              <div className="bg-white rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <FileCheck className="w-5 h-5 text-purple-600" />
                  <h4 className="font-medium text-gray-900">
                    {isRTL ? "السياسات والإجراءات" : "Policies & Procedures"}
                  </h4>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• {isRTL ? "سياسات حماية البيانات" : "Data protection policies"}</li>
                  <li>• {isRTL ? "إجراءات الاستجابة للحوادث" : "Incident response procedures"}</li>
                  <li>• {isRTL ? "إرشادات الموظفين" : "Staff guidelines"}</li>
                </ul>
              </div>
              <div className="bg-white rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Users className="w-5 h-5 text-purple-600" />
                  <h4 className="font-medium text-gray-900">
                    {isRTL ? "الأدوار والمسؤوليات" : "Roles & Responsibilities"}
                  </h4>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• {isRTL ? "مسؤول حماية البيانات" : "Data Protection Officer"}</li>
                  <li>• {isRTL ? "فريق الحوكمة" : "Governance team"}</li>
                  <li>• {isRTL ? "مصفوفة المسؤوليات" : "Responsibility matrix"}</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="outline"
              onClick={() => router.push(`/${lang}/Thiqah/Tasks/Systems/${systemId}/DPIA-TIA`)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              {isRTL ? "العودة إلى DPIA & TIA" : "Return to DPIA & TIA"}
            </Button>
            <Button
              onClick={() => router.push(`/${lang}/Thiqah/Tasks/Systems`)}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {isRTL ? "العودة إلى لوحة الأنظمة" : "Back to Systems Dashboard"}
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
