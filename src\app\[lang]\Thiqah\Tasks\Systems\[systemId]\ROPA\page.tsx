"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Locale } from "@/i18n-config";
import { useRouter } from "next/navigation";
import { ArrowLeft, FileText, Users, Settings, Database, CheckCircle2, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemsService, System } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";
import { SystemTasksJourney, PhaseStatus } from "@/components/SystemTasks/SystemTasksJourney";
import { PersonalDataGroupsTab } from "@/components/ROPA/PersonalDataGroupsTab";
import { ServicesTab } from "@/components/ROPA/ServicesTab";
import { QuestionnaireTab } from "@/components/ROPA/QuestionnaireTab";
import { ROPADetailsTab } from "@/components/ROPA/ROPADetailsTab";
import { motion } from "framer-motion";

interface ROPAPageProps {
  params: Promise<{ lang: Locale; systemId: string }>;
}

export default function ROPAPage({ params }: ROPAPageProps) {
  const [lang, setLang] = useState<string>('');
  const [systemId, setSystemId] = useState<string>('');
  const [system, setSystem] = useState<System | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'personal-data-groups' | 'services' | 'questionnaire' | 'ropa-details'>('personal-data-groups');

  const router = useRouter();
  const { toast } = useToast();

  // Initialize params
  useEffect(() => {
    params.then(({ lang, systemId }) => {
      setLang(lang);
      setSystemId(systemId);
    });
  }, [params]);

  const loadSystemDetails = useCallback(async () => {
    if (!systemId) return;

    try {
      setIsLoading(true);
      const systemData = await SystemsService.getSystem(systemId);
      setSystem(systemData);
    } catch (error) {
      console.error('Error loading system details:', error);
      if (lang) {
        const isRTL = lang === "ar";
        toast({
          title: isRTL ? "خطأ في تحميل البيانات" : "Error loading data",
          description: isRTL ? "فشل في تحميل تفاصيل النظام" : "Failed to load system details",
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
    }
  }, [systemId, lang, toast]);

  useEffect(() => {
    if (systemId && lang) {
      loadSystemDetails();
    }
  }, [systemId, lang, loadSystemDetails]);

  // Don't render until params are loaded
  if (!lang || !systemId) {
    return <div>Loading...</div>;
  }

  const isRTL = lang === "ar";

  // Mock phase status - in real implementation, this would come from the system data
  const phaseStatus: PhaseStatus = {
    dataClassification: 'completed',
    ropa: 'current',
    dpiaAndTia: 'locked',
    pdplGovernance: 'locked',
    knowledgeTransfer: 'locked'
  };

  const tabs = [
    {
      id: 'personal-data-groups' as const,
      label: isRTL ? "مجموعات البيانات الشخصية" : "Personal Data Groups",
      icon: Users,
      description: isRTL ? "تصنيف وتجميع البيانات الشخصية" : "Classify and group personal data"
    },
    {
      id: 'services' as const,
      label: isRTL ? "الخدمات" : "Services",
      icon: Settings,
      description: isRTL ? "خدمات النظام ومعالجة البيانات" : "System services and data processing"
    },
    {
      id: 'questionnaire' as const,
      label: isRTL ? "الاستبيان" : "Questionnaire",
      icon: FileText,
      description: isRTL ? "استبيان تقييم معالجة البيانات" : "Data processing assessment questionnaire"
    },
    {
      id: 'ropa-details' as const,
      label: isRTL ? "تفاصيل ROPA" : "ROPA Details",
      icon: Database,
      description: isRTL ? "التفاصيل النهائية لسجل أنشطة المعالجة" : "Final details for Record of Processing Activities"
    }
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--brand-blue)] mx-auto mb-4"></div>
          <p className="text-gray-600">{isRTL ? "جاري التحميل..." : "Loading..."}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 text-white">
        <div className="px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/${lang}/Thiqah/Tasks/Systems`)}
                className="text-white hover:bg-white/20"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                {isRTL ? "العودة للأنظمة" : "Back to Systems"}
              </Button>
              <div>
                <h1 className="text-2xl font-bold">
                  {isRTL ? "سجل أنشطة المعالجة (ROPA)" : "Record of Processing Activities (ROPA)"}
                </h1>
                <p className="text-white/90">
                  {system?.name || (isRTL ? "جاري التحميل..." : "Loading...")}
                </p>
              </div>
            </div>
            
            {/* Status Indicator */}
            <div className="flex items-center gap-2 bg-white/20 px-4 py-2 rounded-lg">
              <AlertCircle className="w-5 h-5" />
              <span className="font-medium">
                {isRTL ? "المرحلة الحالية" : "Current Phase"}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Journey Navigation */}
      <div className="bg-white border-b border-gray-200 px-8 py-6">
        <SystemTasksJourney
          lang={lang}
          systemId={systemId}
          phaseStatus={phaseStatus}
          currentPhase="ropa"
          showNavigation={false}
        />
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-8">
          <div className="flex space-x-8 overflow-x-auto">
            {tabs.map((tab) => {
              const isActive = activeTab === tab.id;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    flex items-center gap-3 px-4 py-4 border-b-2 transition-all duration-200 whitespace-nowrap
                    ${isActive
                      ? 'border-[var(--brand-blue)] text-[var(--brand-blue)]'
                      : 'border-transparent text-gray-600 hover:text-gray-900 hover:border-gray-300'
                    }
                  `}
                >
                  <tab.icon className="w-5 h-5" />
                  <div className="text-left">
                    <div className="font-medium">{tab.label}</div>
                    <div className="text-xs text-gray-500">{tab.description}</div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="px-8 py-8">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'personal-data-groups' && (
            <PersonalDataGroupsTab 
              systemId={systemId} 
              lang={lang} 
              system={system}
            />
          )}
          {activeTab === 'services' && (
            <ServicesTab 
              systemId={systemId} 
              lang={lang} 
              system={system}
            />
          )}
          {activeTab === 'questionnaire' && (
            <QuestionnaireTab 
              systemId={systemId} 
              lang={lang} 
              system={system}
            />
          )}
          {activeTab === 'ropa-details' && (
            <ROPADetailsTab 
              systemId={systemId} 
              lang={lang} 
              system={system}
            />
          )}
        </motion.div>
      </div>

      {/* Phase Completion Actions */}
      <div className="bg-white border-t border-gray-200 px-8 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CheckCircle2 className="w-5 h-5 text-green-600" />
            <span className="text-sm text-gray-600">
              {isRTL ? "تأكد من إكمال جميع الأقسام قبل المتابعة" : "Ensure all sections are completed before proceeding"}
            </span>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={() => router.push(`/${lang}/Thiqah/Tasks/Systems`)}
            >
              {isRTL ? "حفظ والعودة لاحقاً" : "Save & Return Later"}
            </Button>
            <Button
              className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
              onClick={() => {
                // TODO: Mark ROPA phase as complete and navigate to next phase
                toast({
                  title: isRTL ? "تم إكمال المرحلة" : "Phase Completed",
                  description: isRTL ? "تم إكمال مرحلة ROPA بنجاح" : "ROPA phase completed successfully",
                });
              }}
            >
              {isRTL ? "إكمال مرحلة ROPA" : "Complete ROPA Phase"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
