"use client";

import React from "react";
import { useRouter } from "next/navigation";
import {
  Database,
  FileText,
  Shield,
  BookOpen,
  GraduationCap,
  CheckCircle2,
  Lock,
  ArrowRight,
  ArrowLeft,
  Play
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";

export interface PhaseStatus {
  dataClassification: { status: 'completed' | 'current' | 'locked'; completionRate: number };
  ropa: { status: 'completed' | 'current' | 'locked'; completionRate: number };
  dpiaAndTia: { status: 'completed' | 'current' | 'locked'; completionRate: number };
  pdplGovernance: { status: 'completed' | 'current' | 'locked'; completionRate: number };
  knowledgeTransfer: { status: 'completed' | 'current' | 'locked'; completionRate: number };
}

interface Phase {
  id: keyof PhaseStatus;
  name: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  icon: React.ComponentType<any>;
  route: string;
  color: string;
}

interface SystemTasksJourneyProps {
  lang: string;
  systemId?: string;
  phaseStatus: PhaseStatus;
  currentPhase?: keyof PhaseStatus;
  onPhaseClick?: (phaseId: keyof PhaseStatus) => void;
  showNavigation?: boolean;
}

export function SystemTasksJourney({ 
  lang, 
  systemId, 
  phaseStatus, 
  currentPhase,
  onPhaseClick,
  showNavigation = true 
}: SystemTasksJourneyProps) {
  const router = useRouter();
  const isRTL = lang === "ar";

  const phases: Phase[] = [
    {
      id: 'dataClassification',
      name: {
        en: 'Data Classification',
        ar: 'تصنيف البيانات'
      },
      description: {
        en: 'Classify and categorize system data',
        ar: 'تصنيف وتصنيف بيانات النظام'
      },
      icon: Database,
      route: `/${lang}/Thiqah/Tasks/Systems`,
      color: '#3B82F6' // Blue
    },
    {
      id: 'ropa',
      name: {
        en: 'ROPA',
        ar: 'سجل أنشطة المعالجة'
      },
      description: {
        en: 'Record of Processing Activities',
        ar: 'سجل أنشطة معالجة البيانات'
      },
      icon: FileText,
      route: systemId ? `/${lang}/Thiqah/Tasks/Systems/${systemId}/ROPA` : '#',
      color: '#10B981' // Green
    },
    {
      id: 'dpiaAndTia',
      name: {
        en: 'DPIA & TIA',
        ar: 'تقييم الأثر وتحليل التهديدات'
      },
      description: {
        en: 'Data Protection & Threat Impact Assessment',
        ar: 'تقييم أثر حماية البيانات وتحليل التهديدات'
      },
      icon: Shield,
      route: systemId ? `/${lang}/Thiqah/Tasks/Systems/${systemId}/DPIA-TIA` : '#',
      color: '#F59E0B' // Orange
    },
    {
      id: 'pdplGovernance',
      name: {
        en: 'PDPL Governance',
        ar: 'حوكمة قانون حماية البيانات'
      },
      description: {
        en: 'Personal Data Protection Law Governance',
        ar: 'حوكمة قانون حماية البيانات الشخصية'
      },
      icon: BookOpen,
      route: systemId ? `/${lang}/Thiqah/Tasks/Systems/${systemId}/PDPL-Governance` : '#',
      color: '#8B5CF6' // Purple
    },
    {
      id: 'knowledgeTransfer',
      name: {
        en: 'Knowledge Transfer',
        ar: 'نقل المعرفة'
      },
      description: {
        en: 'Documentation and knowledge handover',
        ar: 'التوثيق ونقل المعرفة'
      },
      icon: GraduationCap,
      route: systemId ? `/${lang}/Thiqah/Tasks/Systems/${systemId}/Knowledge-Transfer` : '#',
      color: '#EF4444' // Red
    }
  ];

  // Calculate if phase should be unlocked based on previous phase completion
  const isPhaseUnlocked = (phaseIndex: number): boolean => {
    if (phaseIndex === 0) return true; // First phase is always unlocked

    const previousPhase = phases[phaseIndex - 1];
    const previousPhaseData = phaseStatus[previousPhase.id];
    return previousPhaseData.completionRate === 100;
  };

  const getPhaseStatus = (phaseIndex: number): 'completed' | 'current' | 'locked' => {
    const phase = phases[phaseIndex];
    const phaseData = phaseStatus[phase.id];

    if (!isPhaseUnlocked(phaseIndex)) return 'locked';
    if (phaseData.completionRate === 100) return 'completed';
    if (phaseData.completionRate > 0) return 'current';

    // Check if this should be the current phase (first incomplete unlocked phase)
    for (let i = 0; i <= phaseIndex; i++) {
      const checkPhase = phases[i];
      const checkPhaseData = phaseStatus[checkPhase.id];
      if (isPhaseUnlocked(i) && checkPhaseData.completionRate < 100) {
        return i === phaseIndex ? 'current' : 'locked';
      }
    }

    return 'locked';
  };

  const handlePhaseClick = (phase: Phase, phaseIndex: number) => {
    const status = getPhaseStatus(phaseIndex);

    if (status === 'locked') {
      return; // Don't allow navigation to locked phases
    }

    if (onPhaseClick) {
      onPhaseClick(phase.id);
    } else if (phase.route !== '#') {
      router.push(phase.route);
    }
  };

  const getCurrentPhaseIndex = () => {
    if (!currentPhase) return -1;
    return phases.findIndex(phase => phase.id === currentPhase);
  };

  const currentPhaseIndex = getCurrentPhaseIndex();

  const overallProgress = Math.round(
    phases.reduce((sum, phase) => sum + phaseStatus[phase.id].completionRate, 0) / phases.length
  );

  const completedPhases = phases.filter((_, index) => getPhaseStatus(index) === 'completed').length;
  const currentPhaseIndex = phases.findIndex((_, index) => getPhaseStatus(index) === 'current');
  const currentPhaseName = currentPhaseIndex >= 0 ? phases[currentPhaseIndex].name[lang as 'en' | 'ar'] : '';

  return (
    <div className="w-full">
      {/* Overall Summary */}
      <div className="bg-gradient-to-r from-[var(--brand-blue)] to-blue-600 rounded-2xl p-8 mb-12 text-white">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Overall Progress */}
          <div className="text-center">
            <div className="text-4xl font-bold mb-2">{overallProgress}%</div>
            <div className="text-blue-100 text-sm uppercase tracking-wider">
              {isRTL ? "التقدم الإجمالي" : "Overall Progress"}
            </div>
          </div>

          {/* Completed Phases */}
          <div className="text-center">
            <div className="text-4xl font-bold mb-2">{completedPhases}/{phases.length}</div>
            <div className="text-blue-100 text-sm uppercase tracking-wider">
              {isRTL ? "المراحل المكتملة" : "Completed Phases"}
            </div>
          </div>

          {/* Current Phase */}
          <div className="text-center">
            <div className="text-lg font-bold mb-2 truncate">{currentPhaseName || (isRTL ? "مكتمل" : "Complete")}</div>
            <div className="text-blue-100 text-sm uppercase tracking-wider">
              {isRTL ? "المرحلة الحالية" : "Current Phase"}
            </div>
          </div>

          {/* Status */}
          <div className="text-center">
            <div className="text-lg font-bold mb-2">
              {overallProgress === 100 ? (isRTL ? "مكتمل" : "Complete") : (isRTL ? "جاري العمل" : "In Progress")}
            </div>
            <div className="text-blue-100 text-sm uppercase tracking-wider">
              {isRTL ? "الحالة" : "Status"}
            </div>
          </div>
        </div>

        {/* Overall Progress Bar */}
        <div className="mt-6">
          <div className="w-full bg-blue-400/30 rounded-full h-3">
            <div
              className="bg-white h-3 rounded-full transition-all duration-1000 ease-out shadow-lg"
              style={{ width: `${overallProgress}%` }}
            />
          </div>
        </div>
      </div>

      {/* Journey Header */}
      <div className="text-center mb-12">
        <h2 className="text-4xl font-bold text-gray-900 mb-4">
          {isRTL ? "رحلة مهام النظام" : "System Tasks Journey"}
        </h2>
        <p className="text-lg text-gray-600 max-w-3xl mx-auto">
          {isRTL
            ? "اتبع هذه المراحل المتسلسلة لإكمال جميع متطلبات النظام بطريقة منهجية ومنظمة"
            : "Follow these sequential phases to complete all system requirements in a systematic and organized manner"
          }
        </p>
      </div>

      {/* Modern Phase Cards with Card Filling */}
      <div className="relative">
        {/* Connection Flow Line */}
        <div className="hidden lg:block absolute top-32 left-0 right-0 h-1 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 z-0 mx-20">
          <div
            className="h-full bg-gradient-to-r from-[var(--brand-blue)] to-green-500 transition-all duration-1000 ease-out"
            style={{
              width: `${(phases.filter((_, i) => getPhaseStatus(i) === 'completed').length / (phases.length - 1)) * 100}%`
            }}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 relative z-10">
          {phases.map((phase, index) => {
            const phaseData = phaseStatus[phase.id];
            const status = getPhaseStatus(index);
            const isClickable = status !== 'locked';
            const completionRate = phaseData.completionRate;

            return (
              <motion.div
                key={phase.id}
                initial={{ opacity: 0, scale: 0.9, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                className="relative group"
              >
                {/* Phase Card with Filling Background */}
                <div
                  onClick={() => isClickable && handlePhaseClick(phase, index)}
                  className={`
                    relative overflow-hidden rounded-3xl border-2 transition-all duration-500 h-72 flex flex-col
                    ${status === 'locked'
                      ? 'border-gray-300 cursor-not-allowed'
                      : 'border-gray-200 cursor-pointer hover:border-[var(--brand-blue)] hover:shadow-2xl hover:scale-105'
                    }
                    ${status === 'current' ? 'ring-4 ring-[var(--brand-blue)] ring-opacity-30 shadow-xl' : ''}
                    ${status === 'completed' ? 'ring-4 ring-green-500 ring-opacity-30 shadow-xl' : ''}
                  `}
                  style={{
                    background: status === 'locked'
                      ? 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)'
                      : `linear-gradient(135deg,
                          ${phase.color}${Math.round(completionRate * 0.15).toString(16).padStart(2, '0')} 0%,
                          ${phase.color}${Math.round(completionRate * 0.25).toString(16).padStart(2, '0')} ${completionRate}%,
                          #ffffff ${completionRate}%,
                          #ffffff 100%)`
                  }}
                >
                  {/* Phase Number Badge */}
                  <div
                    className={`absolute -top-4 -left-4 w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg z-20 shadow-lg
                      ${status === 'locked' ? 'bg-gray-400' : status === 'completed' ? 'bg-green-500' : 'bg-[var(--brand-blue)]'}
                    `}
                  >
                    <span className="text-white">{index + 1}</span>
                  </div>

                  {/* Lock Overlay for Locked Phases */}
                  {status === 'locked' && (
                    <div className="absolute inset-0 bg-gray-500 bg-opacity-20 backdrop-blur-sm rounded-3xl z-10">
                      <div className="absolute top-6 right-6">
                        <Lock className="w-8 h-8 text-gray-500" />
                      </div>
                    </div>
                  )}

                  {/* Content Container */}
                  <div className="relative z-10 p-6 flex flex-col h-full">
                    {/* Phase Icon */}
                    <div className="flex items-center justify-center mb-4">
                      <div
                        className={`w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg transition-all duration-300
                          ${status === 'locked' ? 'bg-gray-200' : 'bg-white'}
                        `}
                      >
                        <phase.icon
                          className={`w-8 h-8 ${status === 'locked' ? 'text-gray-400' : ''}`}
                          style={{ color: status === 'locked' ? undefined : phase.color }}
                        />
                      </div>
                    </div>

                    {/* Phase Title */}
                    <h3 className={`text-lg font-bold text-center mb-3 ${status === 'locked' ? 'text-gray-500' : 'text-gray-900'}`}>
                      {phase.name[lang as 'en' | 'ar']}
                    </h3>

                    {/* Phase Description */}
                    <p className={`text-center text-sm mb-4 flex-1 ${status === 'locked' ? 'text-gray-400' : 'text-gray-600'}`}>
                      {phase.description[lang as 'en' | 'ar']}
                    </p>

                    {/* Progress Section */}
                    <div className="mt-auto">
                      {/* Percentage Display */}
                      <div className="text-center mb-3">
                        <div
                          className={`text-3xl font-bold ${status === 'locked' ? 'text-gray-400' : ''}`}
                          style={{ color: status === 'locked' ? undefined : phase.color }}
                        >
                          {completionRate}%
                        </div>
                        <div className="text-xs text-gray-500 uppercase tracking-wider">
                          {isRTL ? "مكتمل" : "Complete"}
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                        <div
                          className="h-2 rounded-full transition-all duration-1000 ease-out"
                          style={{
                            width: `${completionRate}%`,
                            backgroundColor: status === 'locked' ? '#9CA3AF' : phase.color
                          }}
                        />
                      </div>

                      {/* Status Badge */}
                      <div className="text-center">
                        {status === 'completed' && (
                          <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-green-100 text-green-700 text-sm font-medium">
                            <CheckCircle2 className="w-4 h-4" />
                            {isRTL ? "مكتمل" : "Completed"}
                          </div>
                        )}
                        {status === 'current' && (
                          <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-blue-100 text-blue-700 text-sm font-medium">
                            <Play className="w-4 h-4" />
                            {isRTL ? "جاري العمل" : "In Progress"}
                          </div>
                        )}
                        {status === 'locked' && (
                          <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-gray-100 text-gray-500 text-sm font-medium">
                            <Lock className="w-4 h-4" />
                            {isRTL ? "مقفل" : "Locked"}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Completion Glow Effect */}
                  {status === 'completed' && (
                    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-green-400/20 to-green-600/20 pointer-events-none" />
                  )}

                  {/* Current Phase Pulse Effect */}
                  {status === 'current' && (
                    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-blue-400/10 to-blue-600/10 animate-pulse pointer-events-none" />
                  )}
                </div>

                {/* Connection Arrow */}
                {index < phases.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-20">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center shadow-lg transition-all duration-500
                      ${getPhaseStatus(index) === 'completed' ? 'bg-green-500' : 'bg-gray-300'}
                    `}>
                      {isRTL ? (
                        <ArrowLeft className="w-4 h-4 text-white" />
                      ) : (
                        <ArrowRight className="w-4 h-4 text-white" />
                      )}
                    </div>
                  </div>
                )}
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Navigation Buttons */}
      {showNavigation && currentPhase && (
        <div className="flex justify-between mt-12">
          <Button
            variant="outline"
            onClick={() => {
              const prevIndex = currentPhaseIndex - 1;
              if (prevIndex >= 0) {
                handlePhaseClick(phases[prevIndex], prevIndex);
              }
            }}
            disabled={currentPhaseIndex <= 0}
            className="flex items-center gap-2 px-6 py-3"
          >
            {isRTL ? <ArrowRight className="w-4 h-4" /> : <ArrowLeft className="w-4 h-4" />}
            {isRTL ? "التالي" : "Previous"}
          </Button>

          <Button
            onClick={() => {
              const nextIndex = currentPhaseIndex + 1;
              if (nextIndex < phases.length && getPhaseStatus(nextIndex) !== 'locked') {
                handlePhaseClick(phases[nextIndex], nextIndex);
              }
            }}
            disabled={currentPhaseIndex >= phases.length - 1 || getPhaseStatus(currentPhaseIndex + 1) === 'locked'}
            className="flex items-center gap-2 bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 px-6 py-3"
          >
            {isRTL ? "السابق" : "Next"}
            {isRTL ? <ArrowLeft className="w-4 h-4" /> : <ArrowRight className="w-4 h-4" />}
          </Button>
        </div>
      )}

      {/* Phase Details Summary */}
      <div className="mt-12 grid grid-cols-2 md:grid-cols-5 gap-4">
        {phases.map((phase, index) => {
          const phaseData = phaseStatus[phase.id];
          const status = getPhaseStatus(index);

          return (
            <div key={phase.id} className="text-center p-4 bg-white rounded-xl border border-gray-200 hover:shadow-md transition-all duration-200">
              <div className="text-sm font-medium text-gray-600 mb-2">
                {phase.name[lang as 'en' | 'ar']}
              </div>
              <div
                className="text-2xl font-bold mb-1"
                style={{
                  color: status === 'locked' ? '#9CA3AF' : phase.color
                }}
              >
                {phaseData.completionRate}%
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1">
                <div
                  className="h-1 rounded-full transition-all duration-500"
                  style={{
                    width: `${phaseData.completionRate}%`,
                    backgroundColor: status === 'locked' ? '#9CA3AF' : phase.color
                  }}
                />
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
