"use client";

import React from "react";
import { useRouter } from "next/navigation";
import {
  Database,
  FileText,
  Shield,
  BookOpen,
  GraduationCap,
  CheckCircle2,
  Lock,
  ArrowRight,
  ArrowLeft,
  Play
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";

export interface PhaseStatus {
  dataClassification: { status: 'completed' | 'current' | 'locked'; completionRate: number };
  ropa: { status: 'completed' | 'current' | 'locked'; completionRate: number };
  dpiaAndTia: { status: 'completed' | 'current' | 'locked'; completionRate: number };
  pdplGovernance: { status: 'completed' | 'current' | 'locked'; completionRate: number };
  knowledgeTransfer: { status: 'completed' | 'current' | 'locked'; completionRate: number };
}

interface Phase {
  id: keyof PhaseStatus;
  name: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  icon: React.ComponentType<any>;
  route: string;
  color: string;
}

interface SystemTasksJourneyProps {
  lang: string;
  systemId?: string;
  phaseStatus: PhaseStatus;
  currentPhase?: keyof PhaseStatus;
  onPhaseClick?: (phaseId: keyof PhaseStatus) => void;
  showNavigation?: boolean;
}

export function SystemTasksJourney({ 
  lang, 
  systemId, 
  phaseStatus, 
  currentPhase,
  onPhaseClick,
  showNavigation = true 
}: SystemTasksJourneyProps) {
  const router = useRouter();
  const isRTL = lang === "ar";

  const phases: Phase[] = [
    {
      id: 'dataClassification',
      name: {
        en: 'Data Classification',
        ar: 'تصنيف البيانات'
      },
      description: {
        en: 'Classify and categorize system data',
        ar: 'تصنيف وتصنيف بيانات النظام'
      },
      icon: Database,
      route: `/${lang}/Thiqah/Tasks/Systems`,
      color: '#3B82F6' // Blue
    },
    {
      id: 'ropa',
      name: {
        en: 'ROPA',
        ar: 'سجل أنشطة المعالجة'
      },
      description: {
        en: 'Record of Processing Activities',
        ar: 'سجل أنشطة معالجة البيانات'
      },
      icon: FileText,
      route: systemId ? `/${lang}/Thiqah/Tasks/Systems/${systemId}/ROPA` : '#',
      color: '#10B981' // Green
    },
    {
      id: 'dpiaAndTia',
      name: {
        en: 'DPIA & TIA',
        ar: 'تقييم الأثر وتحليل التهديدات'
      },
      description: {
        en: 'Data Protection & Threat Impact Assessment',
        ar: 'تقييم أثر حماية البيانات وتحليل التهديدات'
      },
      icon: Shield,
      route: systemId ? `/${lang}/Thiqah/Tasks/Systems/${systemId}/DPIA-TIA` : '#',
      color: '#F59E0B' // Orange
    },
    {
      id: 'pdplGovernance',
      name: {
        en: 'PDPL Governance',
        ar: 'حوكمة قانون حماية البيانات'
      },
      description: {
        en: 'Personal Data Protection Law Governance',
        ar: 'حوكمة قانون حماية البيانات الشخصية'
      },
      icon: BookOpen,
      route: systemId ? `/${lang}/Thiqah/Tasks/Systems/${systemId}/PDPL-Governance` : '#',
      color: '#8B5CF6' // Purple
    },
    {
      id: 'knowledgeTransfer',
      name: {
        en: 'Knowledge Transfer',
        ar: 'نقل المعرفة'
      },
      description: {
        en: 'Documentation and knowledge handover',
        ar: 'التوثيق ونقل المعرفة'
      },
      icon: GraduationCap,
      route: systemId ? `/${lang}/Thiqah/Tasks/Systems/${systemId}/Knowledge-Transfer` : '#',
      color: '#EF4444' // Red
    }
  ];

  // Calculate if phase should be unlocked based on previous phase completion
  const isPhaseUnlocked = (phaseIndex: number): boolean => {
    if (phaseIndex === 0) return true; // First phase is always unlocked

    const previousPhase = phases[phaseIndex - 1];
    const previousPhaseData = phaseStatus[previousPhase.id];
    return previousPhaseData.completionRate === 100;
  };

  const getPhaseStatus = (phaseIndex: number): 'completed' | 'current' | 'locked' => {
    const phase = phases[phaseIndex];
    const phaseData = phaseStatus[phase.id];

    if (!isPhaseUnlocked(phaseIndex)) return 'locked';
    if (phaseData.completionRate === 100) return 'completed';
    if (phaseData.completionRate > 0) return 'current';

    // Check if this should be the current phase (first incomplete unlocked phase)
    for (let i = 0; i <= phaseIndex; i++) {
      const checkPhase = phases[i];
      const checkPhaseData = phaseStatus[checkPhase.id];
      if (isPhaseUnlocked(i) && checkPhaseData.completionRate < 100) {
        return i === phaseIndex ? 'current' : 'locked';
      }
    }

    return 'locked';
  };

  const handlePhaseClick = (phase: Phase, phaseIndex: number) => {
    const status = getPhaseStatus(phaseIndex);

    if (status === 'locked') {
      return; // Don't allow navigation to locked phases
    }

    if (onPhaseClick) {
      onPhaseClick(phase.id);
    } else if (phase.route !== '#') {
      router.push(phase.route);
    }
  };

  const getCurrentPhaseIndex = () => {
    if (!currentPhase) return -1;
    return phases.findIndex(phase => phase.id === currentPhase);
  };

  const currentPhaseIndex = getCurrentPhaseIndex();

  return (
    <div className="w-full">
      {/* Journey Header */}
      <div className="text-center mb-12">
        <h2 className="text-4xl font-bold text-gray-900 mb-4">
          {isRTL ? "رحلة مهام النظام" : "System Tasks Journey"}
        </h2>
        <p className="text-lg text-gray-600 max-w-3xl mx-auto">
          {isRTL
            ? "اتبع هذه المراحل المتسلسلة لإكمال جميع متطلبات النظام بطريقة منهجية ومنظمة"
            : "Follow these sequential phases to complete all system requirements in a systematic and organized manner"
          }
        </p>
      </div>

      {/* Large Phase Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-5 gap-6">
        {phases.map((phase, index) => {
          const phaseData = phaseStatus[phase.id];
          const status = getPhaseStatus(index);
          const isClickable = status !== 'locked';
          const completionRate = phaseData.completionRate;

          return (
            <motion.div
              key={phase.id}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.15 }}
              className="relative"
            >
              {/* Connection Line to Next Phase */}
              {index < phases.length - 1 && (
                <div className="hidden xl:block absolute top-1/2 -right-3 w-6 h-0.5 bg-gray-300 z-0">
                  <div
                    className="h-full bg-[var(--brand-blue)] transition-all duration-500"
                    style={{
                      width: status === 'completed' ? '100%' : '0%'
                    }}
                  />
                </div>
              )}

              {/* Phase Card */}
              <div
                onClick={() => isClickable && handlePhaseClick(phase, index)}
                className={`
                  relative bg-white rounded-2xl border-2 p-6 transition-all duration-300 h-80 flex flex-col
                  ${status === 'locked'
                    ? 'border-gray-200 cursor-not-allowed opacity-60'
                    : 'border-gray-200 cursor-pointer hover:border-[var(--brand-blue)] hover:shadow-xl hover:-translate-y-1'
                  }
                  ${status === 'current' ? 'ring-2 ring-[var(--brand-blue)] ring-opacity-50' : ''}
                  ${status === 'completed' ? 'border-green-400' : ''}
                `}
              >
                {/* Phase Number Badge */}
                <div className="absolute -top-3 -left-3 w-8 h-8 bg-[var(--brand-blue)] text-white rounded-full flex items-center justify-center font-bold text-sm z-10">
                  {index + 1}
                </div>

                {/* Lock Overlay for Locked Phases */}
                {status === 'locked' && (
                  <div className="absolute inset-0 bg-gray-100 bg-opacity-80 rounded-2xl flex items-center justify-center z-20">
                    <div className="text-center">
                      <Lock className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500 font-medium">
                        {isRTL ? "مقفل" : "Locked"}
                      </p>
                    </div>
                  </div>
                )}

                {/* Phase Icon */}
                <div className="flex items-center justify-center mb-4">
                  <div
                    className="w-16 h-16 rounded-full flex items-center justify-center"
                    style={{ backgroundColor: `${phase.color}20` }}
                  >
                    <phase.icon
                      className="w-8 h-8"
                      style={{ color: phase.color }}
                    />
                  </div>
                </div>

                {/* Phase Title */}
                <h3 className="text-xl font-bold text-gray-900 text-center mb-2">
                  {phase.name[lang as 'en' | 'ar']}
                </h3>

                {/* Phase Description */}
                <p className="text-gray-600 text-center text-sm mb-6 flex-1">
                  {phase.description[lang as 'en' | 'ar']}
                </p>

                {/* Cup Filling Animation */}
                <div className="relative mb-4">
                  {/* Cup Container */}
                  <div className="relative w-20 h-24 mx-auto">
                    {/* Cup Outline */}
                    <div className="absolute inset-0 border-4 border-gray-300 rounded-b-2xl rounded-t-lg"></div>

                    {/* Cup Handle */}
                    <div className="absolute -right-2 top-4 w-3 h-6 border-4 border-gray-300 border-l-0 rounded-r-lg"></div>

                    {/* Liquid Fill */}
                    <div
                      className="absolute bottom-1 left-1 right-1 rounded-b-xl transition-all duration-1000 ease-out"
                      style={{
                        height: `${(completionRate / 100) * 85}%`,
                        backgroundColor: phase.color,
                        opacity: 0.8
                      }}
                    >
                      {/* Liquid Surface Animation */}
                      <div
                        className="absolute top-0 left-0 right-0 h-1 rounded-full animate-pulse"
                        style={{ backgroundColor: phase.color }}
                      />
                    </div>
                  </div>

                  {/* Percentage Text */}
                  <div className="text-center mt-2">
                    <span
                      className="text-2xl font-bold"
                      style={{ color: phase.color }}
                    >
                      {completionRate}%
                    </span>
                  </div>
                </div>

                {/* Status Badge */}
                <div className="text-center">
                  {status === 'completed' && (
                    <div className="flex items-center justify-center gap-2 text-green-600">
                      <CheckCircle2 className="w-5 h-5" />
                      <span className="font-medium">
                        {isRTL ? "مكتمل" : "Completed"}
                      </span>
                    </div>
                  )}
                  {status === 'current' && (
                    <div className="flex items-center justify-center gap-2 text-[var(--brand-blue)]">
                      <Play className="w-5 h-5" />
                      <span className="font-medium">
                        {isRTL ? "جاري العمل" : "In Progress"}
                      </span>
                    </div>
                  )}
                  {status === 'locked' && (
                    <div className="flex items-center justify-center gap-2 text-gray-400">
                      <Lock className="w-5 h-5" />
                      <span className="font-medium">
                        {isRTL ? "مقفل" : "Locked"}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Navigation Buttons */}
      {showNavigation && currentPhase && (
        <div className="flex justify-between mt-12">
          <Button
            variant="outline"
            onClick={() => {
              const prevIndex = currentPhaseIndex - 1;
              if (prevIndex >= 0) {
                handlePhaseClick(phases[prevIndex], prevIndex);
              }
            }}
            disabled={currentPhaseIndex <= 0}
            className="flex items-center gap-2 px-6 py-3"
          >
            {isRTL ? <ArrowRight className="w-4 h-4" /> : <ArrowLeft className="w-4 h-4" />}
            {isRTL ? "التالي" : "Previous"}
          </Button>

          <Button
            onClick={() => {
              const nextIndex = currentPhaseIndex + 1;
              if (nextIndex < phases.length && getPhaseStatus(nextIndex) !== 'locked') {
                handlePhaseClick(phases[nextIndex], nextIndex);
              }
            }}
            disabled={currentPhaseIndex >= phases.length - 1 || getPhaseStatus(currentPhaseIndex + 1) === 'locked'}
            className="flex items-center gap-2 bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 px-6 py-3"
          >
            {isRTL ? "السابق" : "Next"}
            {isRTL ? <ArrowLeft className="w-4 h-4" /> : <ArrowRight className="w-4 h-4" />}
          </Button>
        </div>
      )}

      {/* Progress Summary */}
      <div className="mt-12 bg-gray-50 rounded-xl p-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {isRTL ? "ملخص التقدم الإجمالي" : "Overall Progress Summary"}
          </h3>

          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {phases.map((phase, index) => {
              const phaseData = phaseStatus[phase.id];
              const status = getPhaseStatus(index);

              return (
                <div key={phase.id} className="text-center">
                  <div className="text-sm font-medium text-gray-600 mb-1">
                    {phase.name[lang as 'en' | 'ar']}
                  </div>
                  <div
                    className="text-2xl font-bold"
                    style={{
                      color: status === 'locked' ? '#9CA3AF' : phase.color
                    }}
                  >
                    {phaseData.completionRate}%
                  </div>
                </div>
              );
            })}
          </div>

          {/* Overall Progress Bar */}
          <div className="mt-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>{isRTL ? "التقدم الإجمالي" : "Overall Progress"}</span>
              <span>
                {Math.round(
                  phases.reduce((sum, phase) => sum + phaseStatus[phase.id].completionRate, 0) / phases.length
                )}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-[var(--brand-blue)] to-green-500 h-3 rounded-full transition-all duration-1000"
                style={{
                  width: `${Math.round(
                    phases.reduce((sum, phase) => sum + phaseStatus[phase.id].completionRate, 0) / phases.length
                  )}%`
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
