"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { 
  Database, 
  FileText, 
  Shield, 
  BookOpen, 
  GraduationCap,
  CheckCircle2,
  Lock,
  ArrowRight,
  ArrowLeft
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";

export interface PhaseStatus {
  dataClassification: 'completed' | 'current' | 'locked';
  ropa: 'completed' | 'current' | 'locked';
  dpiaAndTia: 'completed' | 'current' | 'locked';
  pdplGovernance: 'completed' | 'current' | 'locked';
  knowledgeTransfer: 'completed' | 'current' | 'locked';
}

interface Phase {
  id: keyof PhaseStatus;
  name: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  icon: React.ComponentType<any>;
  route: string;
}

interface SystemTasksJourneyProps {
  lang: string;
  systemId?: string;
  phaseStatus: PhaseStatus;
  currentPhase?: keyof PhaseStatus;
  onPhaseClick?: (phaseId: keyof PhaseStatus) => void;
  showNavigation?: boolean;
}

export function SystemTasksJourney({ 
  lang, 
  systemId, 
  phaseStatus, 
  currentPhase,
  onPhaseClick,
  showNavigation = true 
}: SystemTasksJourneyProps) {
  const router = useRouter();
  const isRTL = lang === "ar";

  const phases: Phase[] = [
    {
      id: 'dataClassification',
      name: {
        en: 'Data Classification',
        ar: 'تصنيف البيانات'
      },
      description: {
        en: 'Classify and categorize system data',
        ar: 'تصنيف وتصنيف بيانات النظام'
      },
      icon: Database,
      route: systemId ? `/${lang}/Thiqah/Tasks/Systems` : `/${lang}/Thiqah/Tasks/Systems`
    },
    {
      id: 'ropa',
      name: {
        en: 'ROPA',
        ar: 'سجل أنشطة المعالجة'
      },
      description: {
        en: 'Record of Processing Activities',
        ar: 'سجل أنشطة معالجة البيانات'
      },
      icon: FileText,
      route: systemId ? `/${lang}/Thiqah/Tasks/Systems/${systemId}/ROPA` : '#'
    },
    {
      id: 'dpiaAndTia',
      name: {
        en: 'DPIA & TIA',
        ar: 'تقييم الأثر وتحليل التهديدات'
      },
      description: {
        en: 'Data Protection & Threat Impact Assessment',
        ar: 'تقييم أثر حماية البيانات وتحليل التهديدات'
      },
      icon: Shield,
      route: systemId ? `/${lang}/Thiqah/Tasks/Systems/${systemId}/DPIA-TIA` : '#'
    },
    {
      id: 'pdplGovernance',
      name: {
        en: 'PDPL Governance',
        ar: 'حوكمة قانون حماية البيانات'
      },
      description: {
        en: 'Personal Data Protection Law Governance',
        ar: 'حوكمة قانون حماية البيانات الشخصية'
      },
      icon: BookOpen,
      route: systemId ? `/${lang}/Thiqah/Tasks/Systems/${systemId}/PDPL-Governance` : '#'
    },
    {
      id: 'knowledgeTransfer',
      name: {
        en: 'Knowledge Transfer',
        ar: 'نقل المعرفة'
      },
      description: {
        en: 'Documentation and knowledge handover',
        ar: 'التوثيق ونقل المعرفة'
      },
      icon: GraduationCap,
      route: systemId ? `/${lang}/Thiqah/Tasks/Systems/${systemId}/Knowledge-Transfer` : '#'
    }
  ];

  const getPhaseStatusColor = (status: 'completed' | 'current' | 'locked') => {
    switch (status) {
      case 'completed':
        return 'bg-green-500 border-green-500 text-white';
      case 'current':
        return 'bg-[var(--brand-blue)] border-[var(--brand-blue)] text-white';
      case 'locked':
        return 'bg-gray-300 border-gray-300 text-gray-500';
      default:
        return 'bg-gray-300 border-gray-300 text-gray-500';
    }
  };

  const getPhaseStatusIcon = (status: 'completed' | 'current' | 'locked', PhaseIcon: React.ComponentType<any>) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="w-6 h-6" />;
      case 'current':
        return <PhaseIcon className="w-6 h-6" />;
      case 'locked':
        return <Lock className="w-6 h-6" />;
      default:
        return <Lock className="w-6 h-6" />;
    }
  };

  const handlePhaseClick = (phase: Phase) => {
    const status = phaseStatus[phase.id];
    
    if (status === 'locked') {
      return; // Don't allow navigation to locked phases
    }

    if (onPhaseClick) {
      onPhaseClick(phase.id);
    } else if (phase.route !== '#') {
      router.push(phase.route);
    }
  };

  const getCurrentPhaseIndex = () => {
    if (!currentPhase) return -1;
    return phases.findIndex(phase => phase.id === currentPhase);
  };

  const currentPhaseIndex = getCurrentPhaseIndex();

  return (
    <div className="w-full">
      {/* Journey Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          {isRTL ? "رحلة مهام النظام" : "System Tasks Journey"}
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          {isRTL 
            ? "اتبع هذه المراحل المتسلسلة لإكمال جميع متطلبات النظام بطريقة منهجية ومنظمة"
            : "Follow these sequential phases to complete all system requirements in a systematic and organized manner"
          }
        </p>
      </div>

      {/* Journey Steps */}
      <div className="relative">
        {/* Progress Line */}
        <div className="absolute top-16 left-0 right-0 h-1 bg-gray-200 z-0" 
             style={{ 
               left: isRTL ? '10%' : '10%', 
               right: isRTL ? '10%' : '10%' 
             }}>
          <div 
            className="h-full bg-[var(--brand-blue)] transition-all duration-500"
            style={{ 
              width: `${(phases.filter((_, index) => phaseStatus[phases[index].id] === 'completed').length / (phases.length - 1)) * 100}%` 
            }}
          />
        </div>

        {/* Phase Cards */}
        <div className={`grid grid-cols-1 md:grid-cols-5 gap-4 relative z-10 ${isRTL ? 'md:grid-flow-col-reverse' : ''}`}>
          {phases.map((phase, index) => {
            const status = phaseStatus[phase.id];
            const isClickable = status !== 'locked';
            
            return (
              <motion.div
                key={phase.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex flex-col items-center"
              >
                {/* Phase Circle */}
                <div
                  onClick={() => isClickable && handlePhaseClick(phase)}
                  className={`
                    relative w-16 h-16 rounded-full border-4 flex items-center justify-center mb-4 transition-all duration-300 z-10
                    ${getPhaseStatusColor(status)}
                    ${isClickable ? 'cursor-pointer hover:scale-110 hover:shadow-lg' : 'cursor-not-allowed'}
                  `}
                >
                  {getPhaseStatusIcon(status, phase.icon)}
                  
                  {/* Step Number */}
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-white border-2 border-gray-300 rounded-full flex items-center justify-center text-xs font-bold text-gray-600">
                    {index + 1}
                  </div>
                </div>

                {/* Phase Info */}
                <div className="text-center max-w-32">
                  <h3 className={`font-bold text-sm mb-1 ${status === 'locked' ? 'text-gray-400' : 'text-gray-900'}`}>
                    {phase.name[lang as 'en' | 'ar']}
                  </h3>
                  <p className={`text-xs ${status === 'locked' ? 'text-gray-400' : 'text-gray-600'}`}>
                    {phase.description[lang as 'en' | 'ar']}
                  </p>
                  
                  {/* Status Badge */}
                  <div className="mt-2">
                    {status === 'completed' && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {isRTL ? "مكتمل" : "Completed"}
                      </span>
                    )}
                    {status === 'current' && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {isRTL ? "جاري" : "Current"}
                      </span>
                    )}
                    {status === 'locked' && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-500">
                        {isRTL ? "مقفل" : "Locked"}
                      </span>
                    )}
                  </div>
                </div>

                {/* Arrow (except for last item) */}
                {index < phases.length - 1 && (
                  <div className="hidden md:block absolute top-8 left-full transform -translate-y-1/2 z-0">
                    {isRTL ? (
                      <ArrowLeft className="w-6 h-6 text-gray-400 -ml-3" />
                    ) : (
                      <ArrowRight className="w-6 h-6 text-gray-400 -mr-3" />
                    )}
                  </div>
                )}
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Navigation Buttons */}
      {showNavigation && currentPhase && (
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={() => {
              const prevIndex = currentPhaseIndex - 1;
              if (prevIndex >= 0) {
                handlePhaseClick(phases[prevIndex]);
              }
            }}
            disabled={currentPhaseIndex <= 0}
            className="flex items-center gap-2"
          >
            {isRTL ? <ArrowRight className="w-4 h-4" /> : <ArrowLeft className="w-4 h-4" />}
            {isRTL ? "التالي" : "Previous"}
          </Button>

          <Button
            onClick={() => {
              const nextIndex = currentPhaseIndex + 1;
              if (nextIndex < phases.length && phaseStatus[phases[nextIndex].id] !== 'locked') {
                handlePhaseClick(phases[nextIndex]);
              }
            }}
            disabled={currentPhaseIndex >= phases.length - 1 || phaseStatus[phases[currentPhaseIndex + 1]?.id] === 'locked'}
            className="flex items-center gap-2 bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
          >
            {isRTL ? "السابق" : "Next"}
            {isRTL ? <ArrowLeft className="w-4 h-4" /> : <ArrowRight className="w-4 h-4" />}
          </Button>
        </div>
      )}
    </div>
  );
}
