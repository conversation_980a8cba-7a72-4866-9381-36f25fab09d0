"use client";

import React, { useState, useEffect } from "react";
import { Locale } from "@/i18n-config";
import { useRouter } from "next/navigation";
import { ArrowLeft, Shield, Lock, AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemTasksJourney, PhaseStatus } from "@/components/SystemTasks/SystemTasksJourney";
import { motion } from "framer-motion";

interface DPIATIAPageProps {
  params: Promise<{ lang: Locale; systemId: string }>;
}

export default function DPIATIAPage({ params }: DPIATIAPageProps) {
  const [lang, setLang] = useState<string>('');
  const [systemId, setSystemId] = useState<string>('');

  const router = useRouter();

  // Initialize params
  useEffect(() => {
    params.then(({ lang, systemId }) => {
      setLang(lang);
      setSystemId(systemId);
    });
  }, [params]);

  // Don't render until params are loaded
  if (!lang || !systemId) {
    return <div>Loading...</div>;
  }

  const isRTL = lang === "ar";

  // Mock phase status - DPIA/TIA is locked
  const phaseStatus: PhaseStatus = {
    dataClassification: { status: 'completed', completionRate: 100 },
    ropa: { status: 'current', completionRate: 75 },
    dpiaAndTia: { status: 'locked', completionRate: 0 },
    pdplGovernance: { status: 'locked', completionRate: 0 },
    knowledgeTransfer: { status: 'locked', completionRate: 0 }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80 text-white">
        <div className="px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/${lang}/Thiqah/Tasks/Systems`)}
                className="text-white hover:bg-white/20"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                {isRTL ? "العودة للأنظمة" : "Back to Systems"}
              </Button>
              <div>
                <h1 className="text-2xl font-bold">
                  {isRTL ? "تقييم الأثر وتحليل التهديدات" : "Data Protection & Threat Impact Assessment"}
                </h1>
                <p className="text-white/90">
                  {isRTL ? "DPIA & TIA" : "DPIA & TIA"}
                </p>
              </div>
            </div>
            
            {/* Status Indicator */}
            <div className="flex items-center gap-2 bg-red-500/20 px-4 py-2 rounded-lg">
              <Lock className="w-5 h-5" />
              <span className="font-medium">
                {isRTL ? "مقفل" : "Locked"}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Journey Navigation */}
      <div className="bg-white border-b border-gray-200 px-8 py-6">
        <SystemTasksJourney
          lang={lang}
          systemId={systemId}
          phaseStatus={phaseStatus}
          currentPhase="dpiaAndTia"
          showNavigation={false}
        />
      </div>

      {/* Locked Content */}
      <div className="px-8 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-2xl mx-auto text-center"
        >
          {/* Lock Icon */}
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Lock className="w-12 h-12 text-gray-400" />
          </div>

          {/* Title */}
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {isRTL ? "هذه المرحلة مقفلة" : "This Phase is Locked"}
          </h2>

          {/* Description */}
          <p className="text-lg text-gray-600 mb-8">
            {isRTL 
              ? "يجب إكمال مرحلة ROPA بنسبة 100% قبل الوصول إلى مرحلة تقييم الأثر وتحليل التهديدات"
              : "You must complete the ROPA phase at 100% before accessing the Data Protection & Threat Impact Assessment phase"
            }
          </p>

          {/* Requirements Card */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <div className="flex items-start gap-4">
              <AlertTriangle className="w-6 h-6 text-orange-500 flex-shrink-0 mt-1" />
              <div className="text-left">
                <h3 className="font-semibold text-gray-900 mb-2">
                  {isRTL ? "متطلبات الوصول" : "Access Requirements"}
                </h3>
                <ul className="text-gray-600 space-y-2">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                    {isRTL ? "إكمال جميع مجموعات البيانات الشخصية" : "Complete all Personal Data Groups"}
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                    {isRTL ? "تحديد جميع الخدمات ومعالجة البيانات" : "Define all Services and Data Processing"}
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                    {isRTL ? "إكمال استبيان تقييم معالجة البيانات" : "Complete Data Processing Assessment Questionnaire"}
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                    {isRTL ? "مراجعة وتأكيد تفاصيل ROPA النهائية" : "Review and confirm final ROPA details"}
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* What's Coming Next */}
          <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-6 mb-8">
            <div className="flex items-center gap-3 mb-4">
              <Shield className="w-6 h-6 text-orange-600" />
              <h3 className="text-lg font-semibold text-gray-900">
                {isRTL ? "ما ينتظرك في هذه المرحلة" : "What Awaits in This Phase"}
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">
                  {isRTL ? "تقييم أثر حماية البيانات (DPIA)" : "Data Protection Impact Assessment (DPIA)"}
                </h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• {isRTL ? "تحليل المخاطر على الخصوصية" : "Privacy risk analysis"}</li>
                  <li>• {isRTL ? "تقييم الضرورة والتناسب" : "Necessity and proportionality assessment"}</li>
                  <li>• {isRTL ? "تدابير الحماية المطلوبة" : "Required protection measures"}</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">
                  {isRTL ? "تحليل تأثير التهديدات (TIA)" : "Threat Impact Analysis (TIA)"}
                </h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• {isRTL ? "تحديد التهديدات الأمنية" : "Security threat identification"}</li>
                  <li>• {isRTL ? "تقييم نقاط الضعف" : "Vulnerability assessment"}</li>
                  <li>• {isRTL ? "خطط التخفيف من المخاطر" : "Risk mitigation plans"}</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="outline"
              onClick={() => router.push(`/${lang}/Thiqah/Tasks/Systems/${systemId}/ROPA`)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              {isRTL ? "العودة إلى ROPA" : "Return to ROPA"}
            </Button>
            <Button
              onClick={() => router.push(`/${lang}/Thiqah/Tasks/Systems`)}
              className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
            >
              {isRTL ? "العودة إلى لوحة الأنظمة" : "Back to Systems Dashboard"}
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
